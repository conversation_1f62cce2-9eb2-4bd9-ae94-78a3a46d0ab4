{"version": 3, "file": "bash.js", "sourceRoot": "", "sources": ["../../../src/core/tools/bash.ts"], "names": [], "mappings": ";;;;;;AAAA,iCAA0C;AAE1C,gDAAwB;AACxB,4CAAoB;AAEP,QAAA,kBAAkB,GAAmB;IAChD,IAAI,EAAE,UAAU;IAChB,QAAQ,EAAE;QACR,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gEA8B+C;QAC5D,UAAU,EAAE;YACV,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,6BAA6B;iBAC3C;gBACD,gBAAgB,EAAE;oBAChB,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,mFAAmF;iBACjG;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,uDAAuD;iBACrE;gBACD,GAAG,EAAE;oBACH,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,yDAAyD;oBACtE,oBAAoB,EAAE;wBACpB,IAAI,EAAE,QAAQ;qBACf;iBACF;aACF;YACD,QAAQ,EAAE,CAAC,SAAS,CAAC;SACtB;KACF;CACF,CAAC;AAEF,MAAa,QAAQ;IACnB,KAAK,CAAC,OAAO,CAAC,OAAwB;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,EACJ,OAAO,EACP,gBAAgB,GAAG,OAAO,CAAC,GAAG,EAAE,EAChC,OAAO,GAAG,KAAK,EACf,GAAG,GAAG,EAAE,EACT,GAAG,OAAO,CAAC;YAEZ,kBAAkB;YAClB,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC5C,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,EAAE;oBACV,KAAK,EAAE,yBAAyB;iBACjC,CAAC;YACJ,CAAC;YAED,4BAA4B;YAC5B,MAAM,WAAW,GAAG,cAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAEnD,gCAAgC;YAChC,MAAM,OAAO,GAAG;gBACd,GAAG,OAAO,CAAC,GAAG;gBACd,GAAG,GAAG;aACP,CAAC;YAEF,oCAAoC;YACpC,MAAM,KAAK,GAAG,YAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,WAAW,CAAC;YACzE,MAAM,SAAS,GAAG,YAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAEpE,kBAAkB;YAClB,MAAM,MAAM,GAAG,MAAM,IAAA,aAAK,EAAC,KAAK,EAAE,CAAC,GAAG,SAAS,EAAE,OAAO,CAAC,EAAE;gBACzD,GAAG,EAAE,WAAW;gBAChB,GAAG,EAAE,OAAO;gBACZ,OAAO;gBACP,QAAQ,EAAE,MAAM;gBAChB,iBAAiB,EAAE,KAAK;gBACxB,MAAM,EAAE,KAAK,CAAC,qCAAqC;aACpD,CAAC,CAAC;YAEH,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE7C,iBAAiB;YACjB,MAAM,MAAM,GAAG;gBACb,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,YAAY,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE;gBACxE,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,YAAY,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE;aACzE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAE/B,MAAM,OAAO,GAAG,MAAM,CAAC,QAAQ,KAAK,CAAC,CAAC;YAEtC,OAAO;gBACL,OAAO;gBACP,MAAM,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,2CAA2C,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC9E,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,iCAAiC,MAAM,CAAC,QAAQ,EAAE;gBAC/E,QAAQ,EAAE;oBACR,QAAQ,EAAE,MAAM,CAAC,QAAQ;oBACzB,aAAa;oBACb,OAAO;oBACP,gBAAgB,EAAE,WAAW;oBAC7B,GAAG,EAAG,MAAc,CAAC,GAAG;iBACzB;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE7C,IAAI,KAAK,YAAY,kBAAU,EAAE,CAAC;gBAChC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE;oBAC1B,KAAK,EAAE,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;oBACnC,QAAQ,EAAE;wBACR,QAAQ,EAAE,KAAK,CAAC,QAAQ;wBACxB,aAAa;wBACb,OAAO,EAAE,OAAO,CAAC,OAAO;wBACxB,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,GAAG,EAAE;wBAC3D,MAAM,EAAE,KAAK,CAAC,MAAM;wBACpB,QAAQ,EAAE,KAAK,CAAC,QAAQ;qBACzB;iBACF,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,qBAAqB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACpF,QAAQ,EAAE;oBACR,aAAa;oBACb,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,GAAG,EAAE;iBAC5D;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,KAAiB;QACxC,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnB,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;YACjB,KAAK,CAAC,IAAI,CAAC,qBAAqB,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,KAAK,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACjC,KAAK,CAAC,IAAI,CAAC,cAAc,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;YACjB,KAAK,CAAC,IAAI,CAAC,iBAAiB,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;YACvE,KAAK,CAAC,IAAI,CAAC,YAAY,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,0BAA0B,CAAC;IACxD,CAAC;IAED,8CAA8C;IAC9C,KAAK,CAAC,aAAa,CAAC,OAAe;QACjC,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,YAAE,CAAC,QAAQ,EAAE,KAAK,OAAO;gBAC5C,CAAC,CAAC,eAAe,OAAO,gCAAgC;gBACxD,CAAC,CAAC,cAAc,OAAO,EAAE,CAAC;YAE5B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;gBAChC,OAAO,EAAE,YAAY;gBACrB,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC,OAAO,CAAC;QACxB,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,kDAAkD;IAClD,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;gBAChC,OAAO,EAAE,YAAE,CAAC,QAAQ,EAAE,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK;gBAClD,OAAO,EAAE,IAAI;aACd,CAAC,CAAC;YAEH,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBACpC,OAAO,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YACvD,CAAC;QACH,CAAC;QAAC,MAAM,CAAC;YACP,4BAA4B;QAC9B,CAAC;QAED,OAAO,OAAO,CAAC,GAAG,EAAE,CAAC;IACvB,CAAC;CACF;AAhKD,4BAgKC"}