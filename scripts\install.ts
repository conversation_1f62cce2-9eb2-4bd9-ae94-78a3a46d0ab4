#!/usr/bin/env node

import { execSync, spawn } from 'child_process';
import { promises as fs } from 'fs';
import path from 'path';
import os from 'os';
import chalk from 'chalk';
import inquirer from 'inquirer';

interface InstallOptions {
  action: 'install' | 'update' | 'uninstall';
  global: boolean;
  force: boolean;
}

class ArienInstaller {
  private platform: string;
  private isWindows: boolean;
  private isWSL: boolean;

  constructor() {
    this.platform = os.platform();
    this.isWindows = this.platform === 'win32';
    this.isWSL = this.checkWSL();
  }

  async run(): Promise<void> {
    try {
      console.clear();
      this.displayBanner();

      const options = await this.getInstallOptions();
      
      switch (options.action) {
        case 'install':
          await this.install(options);
          break;
        case 'update':
          await this.update(options);
          break;
        case 'uninstall':
          await this.uninstall(options);
          break;
      }

    } catch (error) {
      console.log(chalk.red(`❌ Installation failed: ${error instanceof Error ? error.message : String(error)}`));
      process.exit(1);
    }
  }

  private displayBanner(): void {
    const banner = `
╔═══════════════════════════════════════════════════════════════╗
║                                                               ║
║     █████╗ ██████╗ ██╗███████╗███╗   ██╗     █████╗ ██╗      ║
║    ██╔══██╗██╔══██╗██║██╔════╝████╗  ██║    ██╔══██╗██║      ║
║    ███████║██████╔╝██║█████╗  ██╔██╗ ██║    ███████║██║      ║
║    ██╔══██║██╔══██╗██║██╔══╝  ██║╚██╗██║    ██╔══██║██║      ║
║    ██║  ██║██║  ██║██║███████╗██║ ╚████║    ██║  ██║██║      ║
║    ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═══╝    ╚═╝  ╚═╝╚═╝      ║
║                                                               ║
║                    🛠️  INSTALLER v1.0.0 🛠️                   ║
║              Cross-Platform Installation Script               ║
║           Windows 11 WSL | macOS | Linux | Windows           ║
║                                                               ║
╚═══════════════════════════════════════════════════════════════╝
    `;

    console.log(chalk.cyan(banner));
    console.log(chalk.white(`Platform: ${chalk.green(this.getPlatformName())}`));
    console.log(chalk.white(`Node.js: ${chalk.green(process.version)}`));
    console.log(chalk.white(`Architecture: ${chalk.green(process.arch)}\n`));
  }

  private async getInstallOptions(): Promise<InstallOptions> {
    const answers = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to do?',
        choices: [
          { name: '📦 Install Arien AI CLI', value: 'install' },
          { name: '🔄 Update existing installation', value: 'update' },
          { name: '🗑️  Uninstall Arien AI CLI', value: 'uninstall' }
        ]
      },
      {
        type: 'list',
        name: 'scope',
        message: 'Installation scope:',
        choices: [
          { name: '🌍 Global (system-wide)', value: 'global' },
          { name: '👤 Local (current user)', value: 'local' }
        ],
        when: (answers) => answers.action === 'install' || answers.action === 'update'
      },
      {
        type: 'confirm',
        name: 'force',
        message: 'Force installation (overwrite existing)?',
        default: false,
        when: (answers) => answers.action === 'install'
      }
    ]);

    return {
      action: answers.action,
      global: answers.scope === 'global',
      force: answers.force || false
    };
  }

  private async install(options: InstallOptions): Promise<void> {
    console.log(chalk.blue('🚀 Starting installation...\n'));

    // Check prerequisites
    await this.checkPrerequisites();

    // Check if already installed
    if (!options.force && await this.isInstalled()) {
      console.log(chalk.yellow('⚠️  Arien AI CLI is already installed.'));
      const { proceed } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'proceed',
          message: 'Do you want to reinstall?',
          default: false
        }
      ]);

      if (!proceed) {
        console.log(chalk.gray('Installation cancelled.'));
        return;
      }
    }

    // Install dependencies
    await this.installDependencies();

    // Build the project
    await this.buildProject();

    // Install globally or locally
    if (options.global) {
      await this.installGlobally();
    } else {
      await this.installLocally();
    }

    // Verify installation
    await this.verifyInstallation();

    console.log(chalk.green.bold('\n🎉 Installation completed successfully!'));
    this.displayUsageInstructions();
  }

  private async update(options: InstallOptions): Promise<void> {
    console.log(chalk.blue('🔄 Updating Arien AI CLI...\n'));

    if (!await this.isInstalled()) {
      console.log(chalk.red('❌ Arien AI CLI is not installed. Use install option instead.'));
      return;
    }

    // Backup configuration
    await this.backupConfiguration();

    // Update dependencies
    await this.installDependencies();

    // Rebuild project
    await this.buildProject();

    // Reinstall
    if (options.global) {
      await this.installGlobally();
    } else {
      await this.installLocally();
    }

    // Restore configuration
    await this.restoreConfiguration();

    // Verify installation
    await this.verifyInstallation();

    console.log(chalk.green.bold('\n🎉 Update completed successfully!'));
  }

  private async uninstall(options: InstallOptions): Promise<void> {
    console.log(chalk.blue('🗑️  Uninstalling Arien AI CLI...\n'));

    if (!await this.isInstalled()) {
      console.log(chalk.yellow('⚠️  Arien AI CLI is not installed.'));
      return;
    }

    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: 'Are you sure you want to uninstall Arien AI CLI?',
        default: false
      }
    ]);

    if (!confirm) {
      console.log(chalk.gray('Uninstallation cancelled.'));
      return;
    }

    // Remove global installation
    try {
      this.execCommand('npm uninstall -g arien-ai-cli');
      console.log(chalk.green('✅ Removed global installation'));
    } catch {
      console.log(chalk.gray('No global installation found'));
    }

    // Remove configuration (optional)
    const { removeConfig } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'removeConfig',
        message: 'Remove configuration files?',
        default: false
      }
    ]);

    if (removeConfig) {
      await this.removeConfiguration();
    }

    console.log(chalk.green.bold('\n🎉 Uninstallation completed!'));
  }

  private async checkPrerequisites(): Promise<void> {
    console.log(chalk.blue('🔍 Checking prerequisites...'));

    // Check Node.js
    try {
      const nodeVersion = this.execCommand('node --version').trim();
      const majorVersion = parseInt(nodeVersion.slice(1));
      
      if (majorVersion >= 22) {
        console.log(chalk.green(`✅ Node.js ${nodeVersion} (compatible)`));
      } else {
        throw new Error(`Node.js 22+ required, found ${nodeVersion}`);
      }
    } catch (error) {
      throw new Error('Node.js is not installed or not in PATH');
    }

    // Check npm
    try {
      const npmVersion = this.execCommand('npm --version').trim();
      console.log(chalk.green(`✅ npm ${npmVersion}`));
    } catch (error) {
      throw new Error('npm is not installed or not in PATH');
    }

    // Check TypeScript
    try {
      const tsVersion = this.execCommand('npx tsc --version').trim();
      console.log(chalk.green(`✅ ${tsVersion}`));
    } catch (error) {
      console.log(chalk.yellow('⚠️  TypeScript not found, will be installed as dependency'));
    }

    console.log();
  }

  private async installDependencies(): Promise<void> {
    console.log(chalk.blue('📦 Installing dependencies...'));
    
    try {
      this.execCommand('npm install', { stdio: 'inherit' });
      console.log(chalk.green('✅ Dependencies installed'));
    } catch (error) {
      throw new Error('Failed to install dependencies');
    }
  }

  private async buildProject(): Promise<void> {
    console.log(chalk.blue('🔨 Building project...'));
    
    try {
      this.execCommand('npm run build', { stdio: 'inherit' });
      console.log(chalk.green('✅ Project built successfully'));
    } catch (error) {
      throw new Error('Failed to build project');
    }
  }

  private async installGlobally(): Promise<void> {
    console.log(chalk.blue('🌍 Installing globally...'));
    
    try {
      this.execCommand('npm install -g .', { stdio: 'inherit' });
      console.log(chalk.green('✅ Installed globally'));
    } catch (error) {
      throw new Error('Failed to install globally. Try running with sudo/administrator privileges.');
    }
  }

  private async installLocally(): Promise<void> {
    console.log(chalk.blue('👤 Installing locally...'));

    let binDir: string;
    let targetPath: string;
    const sourcePath = path.join(process.cwd(), 'dist', 'index.js');

    if (this.isWindows && !this.isWSL) {
      // Windows installation
      binDir = path.join(os.homedir(), 'AppData', 'Local', 'Programs', 'arien-ai');
      targetPath = path.join(binDir, 'arien.cmd');

      try {
        await fs.mkdir(binDir, { recursive: true });

        // Copy the main file
        const mainTarget = path.join(binDir, 'index.js');
        await fs.copyFile(sourcePath, mainTarget);

        // Create batch file wrapper
        const batchContent = `@echo off
node "%~dp0index.js" %*`;
        await fs.writeFile(targetPath, batchContent, 'utf8');

        console.log(chalk.green(`✅ Installed to ${binDir}`));
        console.log(chalk.yellow(`⚠️  Add ${binDir} to your PATH environment variable`));
        console.log(chalk.gray(`   1. Open System Properties > Environment Variables`));
        console.log(chalk.gray(`   2. Add ${binDir} to your PATH`));
        console.log(chalk.gray(`   3. Restart your terminal`));

      } catch (error) {
        throw new Error(`Failed to install on Windows: ${error}`);
      }
    } else {
      // Unix-like systems (Linux, macOS, WSL)
      binDir = path.join(os.homedir(), '.local', 'bin');
      targetPath = path.join(binDir, 'arien');

      try {
        await fs.mkdir(binDir, { recursive: true });

        // Create shell script wrapper
        const shellScript = `#!/usr/bin/env node
require('${sourcePath.replace(/\\/g, '/')}');`;

        await fs.writeFile(targetPath, shellScript, 'utf8');
        await fs.chmod(targetPath, '755');

        console.log(chalk.green(`✅ Installed to ${targetPath}`));

        // Check if binDir is in PATH
        const currentPath = process.env.PATH || '';
        if (!currentPath.includes(binDir)) {
          console.log(chalk.yellow(`⚠️  Add ${binDir} to your PATH`));

          if (this.isWSL) {
            console.log(chalk.gray(`   Add this line to your ~/.bashrc or ~/.zshrc:`));
            console.log(chalk.gray(`   export PATH="$PATH:${binDir}"`));
          } else if (process.platform === 'darwin') {
            console.log(chalk.gray(`   Add this line to your ~/.zshrc or ~/.bash_profile:`));
            console.log(chalk.gray(`   export PATH="$PATH:${binDir}"`));
          } else {
            console.log(chalk.gray(`   Add this line to your ~/.bashrc:`));
            console.log(chalk.gray(`   export PATH="$PATH:${binDir}"`));
          }
        }

      } catch (error) {
        throw new Error(`Failed to install locally: ${error}`);
      }
    }
  }

  private async verifyInstallation(): Promise<void> {
    console.log(chalk.blue('🔍 Verifying installation...'));
    
    try {
      const version = this.execCommand('arien --version').trim();
      console.log(chalk.green(`✅ Arien AI CLI ${version} is working`));
    } catch (error) {
      console.log(chalk.yellow('⚠️  Installation completed but command not found in PATH'));
    }
  }

  private async isInstalled(): Promise<boolean> {
    try {
      this.execCommand('arien --version');
      return true;
    } catch {
      return false;
    }
  }

  private async backupConfiguration(): Promise<void> {
    // Implementation for backing up user configuration
    console.log(chalk.blue('💾 Backing up configuration...'));
    // This would backup the config files
  }

  private async restoreConfiguration(): Promise<void> {
    // Implementation for restoring user configuration
    console.log(chalk.blue('🔄 Restoring configuration...'));
    // This would restore the config files
  }

  private async removeConfiguration(): Promise<void> {
    console.log(chalk.blue('🗑️  Removing configuration...'));
    // This would remove config files
    console.log(chalk.green('✅ Configuration removed'));
  }

  private checkWSL(): boolean {
    try {
      return this.execCommand('uname -r').includes('microsoft');
    } catch {
      return false;
    }
  }

  private getPlatformName(): string {
    if (this.isWSL) return 'Windows WSL';
    if (this.isWindows) return 'Windows';
    if (this.platform === 'darwin') return 'macOS';
    if (this.platform === 'linux') return 'Linux';
    return this.platform;
  }

  private execCommand(command: string, options: any = {}): string {
    return execSync(command, { encoding: 'utf8', ...options });
  }

  private displayUsageInstructions(): void {
    console.log(chalk.cyan.bold('\n📖 Usage Instructions:'));
    console.log(chalk.white('• Run ') + chalk.green('arien') + chalk.white(' to start the interactive CLI'));
    console.log(chalk.white('• Run ') + chalk.green('arien --help') + chalk.white(' to see all available commands'));
    console.log(chalk.white('• Run ') + chalk.green('arien doctor') + chalk.white(' to check system health'));
    console.log(chalk.white('• Run ') + chalk.green('arien config --show') + chalk.white(' to view configuration\n'));
  }
}

// Run the installer
if (require.main === module) {
  const installer = new ArienInstaller();
  installer.run().catch(console.error);
}

export { ArienInstaller };
