import axios, { AxiosResponse } from 'axios';
import { AIProvider, AIMessage, ToolCall, ToolDefinition } from '@/types';

export interface DeepseekResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string | null;
      tool_calls?: Array<{
        id: string;
        type: 'function';
        function: {
          name: string;
          arguments: string;
        };
      }>;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class DeepseekProvider implements AIProvider {
  public readonly name = 'deepseek';
  public readonly baseUrl = 'https://api.deepseek.com/v1';
  public readonly models = ['deepseek-chat', 'deepseek-reasoner'];
  public readonly defaultModel = 'deepseek-chat';

  constructor(public readonly apiKey: string) {
    if (!apiKey) {
      throw new Error('Deepseek API key is required');
    }
  }

  async sendMessage(
    messages: AIMessage[],
    tools?: ToolDefinition[],
    model: string = this.defaultModel,
    options: {
      temperature?: number;
      maxTokens?: number;
      stream?: boolean;
    } = {}
  ): Promise<AIMessage> {
    try {
      const requestBody = {
        model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content,
          tool_calls: msg.toolCalls,
          tool_call_id: msg.toolCallId
        })),
        tools: tools?.map(tool => ({
          type: tool.type,
          function: tool.function
        })),
        temperature: options.temperature ?? 0.7,
        max_tokens: options.maxTokens ?? 4000,
        stream: options.stream ?? false
      };

      const response: AxiosResponse<DeepseekResponse> = await axios.post(
        `${this.baseUrl}/chat/completions`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      const choice = response.data.choices[0];
      if (!choice) {
        throw new Error('No response choice received from Deepseek');
      }

      const message: AIMessage = {
        role: 'assistant',
        content: choice.message.content || '',
        toolCalls: choice.message.tool_calls?.map(tc => ({
          id: tc.id,
          type: tc.type,
          function: {
            name: tc.function.name,
            arguments: tc.function.arguments
          }
        }))
      };

      return message;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const status = error.response?.status;
        const message = error.response?.data?.error?.message || error.message;
        
        if (status === 401) {
          throw new Error('Invalid Deepseek API key');
        } else if (status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        } else if (status === 500) {
          throw new Error('Deepseek server error. Please try again.');
        } else {
          throw new Error(`Deepseek API error: ${message}`);
        }
      }
      throw error;
    }
  }

  async streamMessage(
    messages: AIMessage[],
    tools?: ToolDefinition[],
    model: string = this.defaultModel,
    onChunk?: (chunk: string) => void
  ): Promise<AIMessage> {
    try {
      const requestBody = {
        model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content,
          tool_calls: msg.toolCalls,
          tool_call_id: msg.toolCallId
        })),
        tools: tools?.map(tool => ({
          type: tool.type,
          function: tool.function
        })),
        temperature: 0.7,
        max_tokens: 4000,
        stream: true
      };

      const response = await axios.post(
        `${this.baseUrl}/chat/completions`,
        requestBody,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json'
          },
          responseType: 'stream',
          timeout: 60000
        }
      );

      let fullContent = '';
      let toolCalls: ToolCall[] = [];

      return new Promise((resolve, reject) => {
        response.data.on('data', (chunk: Buffer) => {
          const lines = chunk.toString().split('\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                resolve({
                  role: 'assistant',
                  content: fullContent,
                  toolCalls: toolCalls.length > 0 ? toolCalls : undefined
                });
                return;
              }

              try {
                const parsed = JSON.parse(data);
                const delta = parsed.choices?.[0]?.delta;
                
                if (delta?.content) {
                  fullContent += delta.content;
                  onChunk?.(delta.content);
                }

                if (delta?.tool_calls) {
                  // Handle tool calls in streaming
                  for (const tc of delta.tool_calls) {
                    if (tc.function) {
                      toolCalls.push({
                        id: tc.id,
                        type: 'function',
                        function: {
                          name: tc.function.name,
                          arguments: tc.function.arguments
                        }
                      });
                    }
                  }
                }
              } catch (parseError) {
                // Ignore parsing errors for incomplete chunks
              }
            }
          }
        });

        response.data.on('error', (error: Error) => {
          reject(error);
        });

        response.data.on('end', () => {
          resolve({
            role: 'assistant',
            content: fullContent,
            toolCalls: toolCalls.length > 0 ? toolCalls : undefined
          });
        });
      });
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const message = error.response?.data?.error?.message || error.message;
        throw new Error(`Deepseek streaming error: ${message}`);
      }
      throw error;
    }
  }

  validateModel(model: string): boolean {
    return this.models.includes(model);
  }

  getAvailableModels(): string[] {
    return [...this.models];
  }
}
