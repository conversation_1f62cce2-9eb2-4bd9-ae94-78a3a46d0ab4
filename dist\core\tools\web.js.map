{"version": 3, "file": "web.js", "sourceRoot": "", "sources": ["../../../src/core/tools/web.ts"], "names": [], "mappings": ";;;;;;AACA,kDAA0B;AAEb,QAAA,iBAAiB,GAAmB;IAC/C,IAAI,EAAE,UAAU;IAChB,QAAQ,EAAE;QACR,IAAI,EAAE,KAAK;QACX,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+DA8B8C;QAC3D,UAAU,EAAE;YACV,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,KAAK,EAAE;oBACL,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,6CAA6C;iBAC3D;gBACD,UAAU,EAAE;oBACV,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,+DAA+D;iBAC7E;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,+DAA+D;iBAC7E;aACF;YACD,QAAQ,EAAE,CAAC,OAAO,CAAC;SACpB;KACF;CACF,CAAC;AAoCF,MAAa,OAAO;IACD,OAAO,GAAG,6BAA6B,CAAC;IACxC,SAAS,GAAG,oBAAoB,CAAC;IAElD,KAAK,CAAC,OAAO,CAAC,OAAuB;QACnC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,EACJ,KAAK,EACL,UAAU,GAAG,CAAC,EACd,OAAO,GAAG,KAAK,EAChB,GAAG,OAAO,CAAC;YAEZ,iBAAiB;YACjB,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACxC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,EAAE;oBACV,KAAK,EAAE,8BAA8B;iBACtC,CAAC;YACJ,CAAC;YAED,iBAAiB;YACjB,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YAElE,iBAAiB;YACjB,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;YAC1E,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE7C,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM;gBACN,QAAQ,EAAE;oBACR,KAAK;oBACL,YAAY,EAAE,aAAa,CAAC,MAAM;oBAClC,UAAU;oBACV,aAAa;oBACb,YAAY,EAAE,YAAY;iBAC3B;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,sBAAsB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACrF,QAAQ,EAAE;oBACR,KAAK,EAAE,OAAO,CAAC,KAAK;oBACpB,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;iBACtC;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,KAAa,EAAE,OAAe;QAC3D,IAAI,CAAC;YACH,oCAAoC;YACpC,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE;gBAC7C,MAAM,EAAE;oBACN,CAAC,EAAE,KAAK;oBACR,MAAM,EAAE,MAAM;oBACd,OAAO,EAAE,GAAG;oBACZ,aAAa,EAAE,GAAG;iBACnB;gBACD,OAAO,EAAE;oBACP,YAAY,EAAE,IAAI,CAAC,SAAS;iBAC7B;gBACD,OAAO;aACR,CAAC,CAAC;YAEH,MAAM,IAAI,GAAuB,QAAQ,CAAC,IAAI,CAAC;YAC/C,MAAM,OAAO,GAAuB,EAAE,CAAC;YAEvC,sCAAsC;YACtC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,OAAO,CAAC,IAAI,CAAC;oBACX,KAAK,EAAE,gBAAgB;oBACvB,GAAG,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;oBAC3B,OAAO,EAAE,IAAI,CAAC,MAAM;iBACrB,CAAC,CAAC;YACL,CAAC;YAED,gCAAgC;YAChC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACvC,OAAO,CAAC,IAAI,CAAC;oBACX,KAAK,EAAE,IAAI,CAAC,OAAO,IAAI,UAAU;oBACjC,GAAG,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;oBAC3B,OAAO,EAAE,IAAI,CAAC,YAAY;iBAC3B,CAAC,CAAC;YACL,CAAC;YAED,kCAAkC;YAClC,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;gBACpB,OAAO,CAAC,IAAI,CAAC;oBACX,KAAK,EAAE,YAAY;oBACnB,GAAG,EAAE,IAAI,CAAC,aAAa,IAAI,EAAE;oBAC7B,OAAO,EAAE,IAAI,CAAC,UAAU;iBACzB,CAAC,CAAC;YACL,CAAC;YAED,yBAAyB;YACzB,IAAI,IAAI,CAAC,aAAa,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC5D,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;oBACnD,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;wBACjC,OAAO,CAAC,IAAI,CAAC;4BACX,KAAK,EAAE,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC;4BAC5C,GAAG,EAAE,KAAK,CAAC,QAAQ;4BACnB,OAAO,EAAE,KAAK,CAAC,IAAI;yBACpB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;YAED,kBAAkB;YAClB,IAAI,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAChD,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;oBAC9C,IAAI,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;wBACnC,OAAO,CAAC,IAAI,CAAC;4BACX,KAAK,EAAE,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,IAAI,CAAC;4BAC7C,GAAG,EAAE,MAAM,CAAC,QAAQ;4BACpB,OAAO,EAAE,MAAM,CAAC,IAAI;yBACrB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;YAED,wDAAwD;YACxD,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACnD,CAAC;YAED,OAAO,OAAO,CAAC;QAEjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,2CAA2C;YAC3C,IAAI,eAAK,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBAC/D,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;YACnD,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,KAAa,EAAE,QAAgB;QAC1D,4DAA4D;QAC5D,sDAAsD;QACtD,OAAO,CAAC;gBACN,KAAK,EAAE,mBAAmB;gBAC1B,GAAG,EAAE,6BAA6B,kBAAkB,CAAC,KAAK,CAAC,EAAE;gBAC7D,OAAO,EAAE,eAAe,KAAK,kHAAkH;aAChJ,CAAC,CAAC;IACL,CAAC;IAEO,oBAAoB,CAAC,IAAY;QACvC,gFAAgF;QAChF,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACzB,CAAC;QAED,qCAAqC;QACrC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QACrC,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;YACtB,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACzB,CAAC;QAED,oCAAoC;QACpC,OAAO,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;IACjE,CAAC;IAEO,mBAAmB,CAAC,OAA2B,EAAE,KAAa,EAAE,UAAkB;QACxF,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,gCAAgC,KAAK,4DAA4D,CAAC;QAC3G,CAAC;QAED,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,CAAC,IAAI,CAAC,2BAA2B,KAAK,KAAK,CAAC,CAAC;QAEnD,MAAM,cAAc,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;QAEpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/C,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YACjC,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC;gBAE7C,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC;oBACf,MAAM,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;gBACrC,CAAC;gBAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG;wBACzC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;wBAC1C,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;oBACnB,MAAM,CAAC,IAAI,CAAC,SAAS,OAAO,EAAE,CAAC,CAAC;gBAClC,CAAC;gBAED,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,6BAA6B;YAChD,CAAC;QACH,CAAC;QAED,IAAI,OAAO,CAAC,MAAM,GAAG,UAAU,EAAE,CAAC;YAChC,MAAM,CAAC,IAAI,CAAC,WAAW,OAAO,CAAC,MAAM,GAAG,UAAU,yBAAyB,CAAC,CAAC;QAC/E,CAAC;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAED,0CAA0C;IAC1C,aAAa,CAAC,KAAa;QACzB,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;QAC1D,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YACvB,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,qCAAqC,EAAE,CAAC;QACxE,CAAC;QAED,+CAA+C;QAC/C,MAAM,gBAAgB,GAAG,aAAa,CAAC;QACvC,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,mDAAmD,EAAE,CAAC;QACtF,CAAC;QAED,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACzB,CAAC;IAED,gDAAgD;IAChD,wBAAwB,CAAC,KAAa;QACpC,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,WAAW,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACzB,WAAW,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,KAAK,EAAE,CAAC;YAClC,WAAW,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7B,WAAW,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QACrE,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;CACF;AAxPD,0BAwPC"}