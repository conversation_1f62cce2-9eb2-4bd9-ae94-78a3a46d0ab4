{"version": 3, "file": "deepseek.js", "sourceRoot": "", "sources": ["../../../../src/core/ai/providers/deepseek.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA6C;AA+B7C,MAAa,gBAAgB;IAMC;IALZ,IAAI,GAAG,UAAU,CAAC;IAClB,OAAO,GAAG,6BAA6B,CAAC;IACxC,MAAM,GAAG,CAAC,eAAe,EAAE,mBAAmB,CAAC,CAAC;IAChD,YAAY,GAAG,eAAe,CAAC;IAE/C,YAA4B,MAAc;QAAd,WAAM,GAAN,MAAM,CAAQ;QACxC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CACf,QAAqB,EACrB,KAAwB,EACxB,QAAgB,IAAI,CAAC,YAAY,EACjC,UAII,EAAE;QAEN,IAAI,CAAC;YACH,MAAM,WAAW,GAAG;gBAClB,KAAK;gBACL,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBAC7B,IAAI,EAAE,GAAG,CAAC,IAAI;oBACd,OAAO,EAAE,GAAG,CAAC,OAAO;oBACpB,UAAU,EAAE,GAAG,CAAC,SAAS;oBACzB,YAAY,EAAE,GAAG,CAAC,UAAU;iBAC7B,CAAC,CAAC;gBACH,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACzB,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;iBACxB,CAAC,CAAC;gBACH,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,GAAG;gBACvC,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI;gBACrC,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,KAAK;aAChC,CAAC;YAEF,MAAM,QAAQ,GAAoC,MAAM,eAAK,CAAC,IAAI,CAChE,GAAG,IAAI,CAAC,OAAO,mBAAmB,EAClC,WAAW,EACX;gBACE,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,MAAM,EAAE;oBACxC,cAAc,EAAE,kBAAkB;iBACnC;gBACD,OAAO,EAAE,KAAK;aACf,CACF,CAAC;YAEF,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACxC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;YAC/D,CAAC;YAED,MAAM,OAAO,GAAc;gBACzB,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE;gBACrC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBAC/C,EAAE,EAAE,EAAE,CAAC,EAAE;oBACT,IAAI,EAAE,EAAE,CAAC,IAAI;oBACb,QAAQ,EAAE;wBACR,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI;wBACtB,SAAS,EAAE,EAAE,CAAC,QAAQ,CAAC,SAAS;qBACjC;iBACF,CAAC,CAAC;aACJ,CAAC;YAEF,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,eAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9B,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC;gBACtC,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;gBAEtE,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;oBACnB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;gBAC9C,CAAC;qBAAM,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;oBAC1B,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;gBAClE,CAAC;qBAAM,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;oBAC1B,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;gBAC9D,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,uBAAuB,OAAO,EAAE,CAAC,CAAC;gBACpD,CAAC;YACH,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CACjB,QAAqB,EACrB,KAAwB,EACxB,QAAgB,IAAI,CAAC,YAAY,EACjC,OAAiC;QAEjC,IAAI,CAAC;YACH,MAAM,WAAW,GAAG;gBAClB,KAAK;gBACL,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBAC7B,IAAI,EAAE,GAAG,CAAC,IAAI;oBACd,OAAO,EAAE,GAAG,CAAC,OAAO;oBACpB,UAAU,EAAE,GAAG,CAAC,SAAS;oBACzB,YAAY,EAAE,GAAG,CAAC,UAAU;iBAC7B,CAAC,CAAC;gBACH,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBACzB,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;iBACxB,CAAC,CAAC;gBACH,WAAW,EAAE,GAAG;gBAChB,UAAU,EAAE,IAAI;gBAChB,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAC/B,GAAG,IAAI,CAAC,OAAO,mBAAmB,EAClC,WAAW,EACX;gBACE,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,MAAM,EAAE;oBACxC,cAAc,EAAE,kBAAkB;iBACnC;gBACD,YAAY,EAAE,QAAQ;gBACtB,OAAO,EAAE,KAAK;aACf,CACF,CAAC;YAEF,IAAI,WAAW,GAAG,EAAE,CAAC;YACrB,IAAI,SAAS,GAAe,EAAE,CAAC;YAE/B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAa,EAAE,EAAE;oBACzC,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBAE3C,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;wBACzB,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;4BAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;4BAC3B,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;gCACtB,OAAO,CAAC;oCACN,IAAI,EAAE,WAAW;oCACjB,OAAO,EAAE,WAAW;oCACpB,SAAS,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;iCACxD,CAAC,CAAC;gCACH,OAAO;4BACT,CAAC;4BAED,IAAI,CAAC;gCACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gCAChC,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;gCAEzC,IAAI,KAAK,EAAE,OAAO,EAAE,CAAC;oCACnB,WAAW,IAAI,KAAK,CAAC,OAAO,CAAC;oCAC7B,OAAO,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gCAC3B,CAAC;gCAED,IAAI,KAAK,EAAE,UAAU,EAAE,CAAC;oCACtB,iCAAiC;oCACjC,KAAK,MAAM,EAAE,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;wCAClC,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;4CAChB,SAAS,CAAC,IAAI,CAAC;gDACb,EAAE,EAAE,EAAE,CAAC,EAAE;gDACT,IAAI,EAAE,UAAU;gDAChB,QAAQ,EAAE;oDACR,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI;oDACtB,SAAS,EAAE,EAAE,CAAC,QAAQ,CAAC,SAAS;iDACjC;6CACF,CAAC,CAAC;wCACL,CAAC;oCACH,CAAC;gCACH,CAAC;4BACH,CAAC;4BAAC,OAAO,UAAU,EAAE,CAAC;gCACpB,8CAA8C;4BAChD,CAAC;wBACH,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;oBACzC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC;gBAEH,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;oBAC3B,OAAO,CAAC;wBACN,IAAI,EAAE,WAAW;wBACjB,OAAO,EAAE,WAAW;wBACpB,SAAS,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;qBACxD,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,eAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;gBACtE,MAAM,IAAI,KAAK,CAAC,6BAA6B,OAAO,EAAE,CAAC,CAAC;YAC1D,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,aAAa,CAAC,KAAa;QACzB,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED,kBAAkB;QAChB,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC;CACF;AA7MD,4CA6MC"}