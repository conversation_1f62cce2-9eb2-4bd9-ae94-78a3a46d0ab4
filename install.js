#!/usr/bin/env node

/**
 * Arien AI CLI - Standalone Installation Script
 * Cross-platform installer for Windows 11 WSL, macOS, Linux, and Windows
 * 
 * Usage:
 *   node install.js
 *   curl -fsSL https://raw.githubusercontent.com/arien-ai/arien-ai-cli/main/install.js | node
 */

const { execSync, spawn } = require('child_process');
const { promises: fs } = require('fs');
const path = require('path');
const os = require('os');
const https = require('https');

class ArienInstaller {
  constructor() {
    this.platform = os.platform();
    this.isWindows = this.platform === 'win32';
    this.isWSL = this.checkWSL();
    this.tempDir = path.join(os.tmpdir(), 'arien-ai-install');
  }

  async run() {
    try {
      console.clear();
      this.displayBanner();
      
      console.log('🚀 Starting Arien AI CLI installation...\n');
      
      // Check prerequisites
      await this.checkPrerequisites();
      
      // Get installation options
      const options = await this.getInstallOptions();
      
      // Download and install
      await this.downloadAndInstall(options);
      
      console.log('\n🎉 Installation completed successfully!');
      this.displayUsageInstructions();
      
    } catch (error) {
      console.error(`\n❌ Installation failed: ${error.message}`);
      process.exit(1);
    }
  }

  displayBanner() {
    const banner = `
╔═══════════════════════════════════════════════════════════════╗
║                                                               ║
║     █████╗ ██████╗ ██╗███████╗███╗   ██╗     █████╗ ██╗      ║
║    ██╔══██╗██╔══██╗██║██╔════╝████╗  ██║    ██╔══██╗██║      ║
║    ███████║██████╔╝██║█████╗  ██╔██╗ ██║    ███████║██║      ║
║    ██╔══██║██╔══██╗██║██╔══╝  ██║╚██╗██║    ██╔══██║██║      ║
║    ██║  ██║██║  ██║██║███████╗██║ ╚████║    ██║  ██║██║      ║
║    ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═══╝    ╚═╝  ╚═╝╚═╝      ║
║                                                               ║
║                    🛠️  INSTALLER v1.0.0 🛠️                   ║
║              Cross-Platform Installation Script               ║
║           Windows 11 WSL | macOS | Linux | Windows           ║
║                                                               ║
╚═══════════════════════════════════════════════════════════════╝`;
    
    console.log(banner);
    console.log(`Platform: ${this.getPlatformName()}`);
    console.log(`Node.js: ${process.version}`);
    console.log(`Architecture: ${process.arch}\n`);
  }

  async checkPrerequisites() {
    console.log('🔍 Checking prerequisites...');
    
    // Check Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1));
    
    if (majorVersion >= 22) {
      console.log(`✅ Node.js ${nodeVersion} (compatible)`);
    } else {
      throw new Error(`Node.js 22+ required, found ${nodeVersion}`);
    }
    
    // Check npm
    try {
      const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
      console.log(`✅ npm ${npmVersion}`);
    } catch (error) {
      throw new Error('npm is not installed or not in PATH');
    }
    
    console.log();
  }

  async getInstallOptions() {
    // Simple installation - always install globally if possible, locally otherwise
    try {
      execSync('npm --version', { stdio: 'ignore' });
      return { global: true };
    } catch {
      return { global: false };
    }
  }

  async downloadAndInstall(options) {
    console.log('📦 Downloading Arien AI CLI...');
    
    // Create temp directory
    await fs.mkdir(this.tempDir, { recursive: true });
    
    try {
      if (options.global) {
        // Install globally via npm
        console.log('🌍 Installing globally via npm...');
        execSync('npm install -g arien-ai-cli', { stdio: 'inherit' });
      } else {
        // Download and install locally
        await this.downloadAndInstallLocally();
      }
      
      // Verify installation
      await this.verifyInstallation();
      
    } finally {
      // Cleanup temp directory
      try {
        await fs.rmdir(this.tempDir, { recursive: true });
      } catch {
        // Ignore cleanup errors
      }
    }
  }

  async downloadAndInstallLocally() {
    console.log('👤 Installing locally...');
    
    // This is a simplified version - in a real implementation,
    // you would download the actual package from npm or GitHub
    throw new Error('Local installation not yet implemented. Please use: npm install -g arien-ai-cli');
  }

  async verifyInstallation() {
    console.log('🔍 Verifying installation...');
    
    try {
      const version = execSync('arien --version', { encoding: 'utf8' }).trim();
      console.log(`✅ Arien AI CLI ${version} is working`);
    } catch (error) {
      console.log('⚠️  Installation completed but command not found in PATH');
      console.log('   You may need to restart your terminal or update your PATH');
    }
  }

  checkWSL() {
    try {
      const release = execSync('uname -r', { encoding: 'utf8' });
      return release.toLowerCase().includes('microsoft');
    } catch {
      return false;
    }
  }

  getPlatformName() {
    if (this.isWSL) return 'Windows WSL';
    if (this.isWindows) return 'Windows';
    if (this.platform === 'darwin') return 'macOS';
    if (this.platform === 'linux') return 'Linux';
    return this.platform;
  }

  displayUsageInstructions() {
    console.log('\n📖 Usage Instructions:');
    console.log('• Run "arien" to start the interactive CLI');
    console.log('• Run "arien --help" to see all available commands');
    console.log('• Run "arien doctor" to check system health');
    console.log('• Run "arien config --show" to view configuration\n');
    
    console.log('🔗 More information:');
    console.log('• Documentation: https://github.com/arien-ai/arien-ai-cli');
    console.log('• Issues: https://github.com/arien-ai/arien-ai-cli/issues');
  }
}

// Run the installer if this script is executed directly
if (require.main === module) {
  const installer = new ArienInstaller();
  installer.run().catch(console.error);
}

module.exports = { ArienInstaller };
