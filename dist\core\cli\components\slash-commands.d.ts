import { CLIConfig } from '../../../types';
export declare class SlashCommandsComponent {
    private commands;
    constructor();
    private initializeCommands;
    handleSlashCommand(input: string, config: CLIConfig): Promise<{
        handled: boolean;
        newConfig?: CLIConfig;
        shouldExit?: boolean;
        shouldClear?: boolean;
    }>;
    showCommandPicker(): Promise<string | null>;
    private executeHelp;
    private executeProvider;
    private executeModel;
    private executeClear;
    private executeExit;
    private executeStatus;
    private executeTools;
    getAvailableCommands(): string[];
    getCommandDescription(commandName: string): string | undefined;
}
//# sourceMappingURL=slash-commands.d.ts.map