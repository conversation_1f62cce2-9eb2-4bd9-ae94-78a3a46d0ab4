"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SlashCommandsComponent = void 0;
const inquirer_1 = __importDefault(require("inquirer"));
const login_1 = require("./login");
const deepseek_1 = require("../../../core/ai/providers/deepseek");
const ollama_1 = require("../../../core/ai/providers/ollama");
const chalk_1 = __importDefault(require("chalk"));
class SlashCommandsComponent {
    commands = new Map();
    constructor() {
        this.initializeCommands();
    }
    initializeCommands() {
        this.commands.set('help', {
            name: 'help',
            description: 'Show help information',
            execute: this.executeHelp.bind(this)
        });
        this.commands.set('provider', {
            name: 'provider',
            description: 'Change AI provider settings',
            execute: this.executeProvider.bind(this)
        });
        this.commands.set('model', {
            name: 'model',
            description: 'Change AI model',
            execute: this.executeModel.bind(this)
        });
        this.commands.set('clear', {
            name: 'clear',
            description: 'Clear chat history',
            execute: this.executeClear.bind(this)
        });
        this.commands.set('exit', {
            name: 'exit',
            description: 'Exit the application',
            execute: this.executeExit.bind(this)
        });
        this.commands.set('status', {
            name: 'status',
            description: 'Show current configuration status',
            execute: this.executeStatus.bind(this)
        });
        this.commands.set('tools', {
            name: 'tools',
            description: 'List available tools',
            execute: this.executeTools.bind(this)
        });
    }
    async handleSlashCommand(input, config) {
        const trimmed = input.trim();
        if (!trimmed.startsWith('/')) {
            return { handled: false };
        }
        const parts = trimmed.slice(1).split(' ');
        const commandName = parts[0]?.toLowerCase();
        const args = parts.slice(1);
        if (!commandName) {
            console.log(chalk_1.default.red('❌ No command specified'));
            return { handled: true };
        }
        const command = this.commands.get(commandName);
        if (!command) {
            console.log(chalk_1.default.red(`❌ Unknown command: /${commandName}`));
            console.log(chalk_1.default.gray('Type /help to see available commands'));
            return { handled: true };
        }
        try {
            const result = await command.execute(args, config);
            return { handled: true, ...result };
        }
        catch (error) {
            console.log(chalk_1.default.red(`❌ Command failed: ${error instanceof Error ? error.message : String(error)}`));
            return { handled: true };
        }
    }
    async showCommandPicker() {
        const choices = Array.from(this.commands.values()).map(cmd => ({
            name: `/${cmd.name} - ${cmd.description}`,
            value: cmd.name
        }));
        const answer = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'command',
                message: 'Select a command:',
                choices: [
                    ...choices,
                    { name: 'Cancel', value: null }
                ]
            }
        ]);
        return answer.command;
    }
    async executeHelp(_args) {
        console.log(chalk_1.default.cyan.bold('\n📖 Available Slash Commands\n'));
        for (const [name, command] of this.commands) {
            console.log(chalk_1.default.white(`/${name}`) + chalk_1.default.gray(` - ${command.description}`));
        }
        console.log(chalk_1.default.gray('\nTip: Type "/" and press Tab for command completion\n'));
        return {};
    }
    async executeProvider(_args, _config) {
        console.log(chalk_1.default.blue('🔧 Reconfiguring AI Provider...\n'));
        const loginComponent = new login_1.LoginComponent();
        const newConfig = await loginComponent.configure();
        return { newConfig };
    }
    async executeModel(_args, config) {
        if (!config) {
            console.log(chalk_1.default.red('❌ No configuration available'));
            return {};
        }
        console.log(chalk_1.default.blue('🔧 Changing AI Model...\n'));
        let availableModels = [];
        try {
            if (config.provider === 'deepseek') {
                availableModels = ['deepseek-chat', 'deepseek-reasoner'];
            }
            else if (config.provider === 'ollama') {
                const provider = new ollama_1.OllamaProvider(config.baseUrl);
                await provider.initialize();
                availableModels = provider.getAvailableModels();
            }
            if (availableModels.length === 0) {
                console.log(chalk_1.default.yellow('⚠️  No models available'));
                return {};
            }
            const answer = await inquirer_1.default.prompt([
                {
                    type: 'list',
                    name: 'model',
                    message: 'Select a model:',
                    choices: availableModels.map(model => ({
                        name: model === config.model ? `${model} (current)` : model,
                        value: model
                    }))
                }
            ]);
            if (answer.model === config.model) {
                console.log(chalk_1.default.gray('Model unchanged'));
                return {};
            }
            const newConfig = { ...config, model: answer.model };
            console.log(chalk_1.default.green(`✅ Model changed to: ${answer.model}`));
            return { newConfig };
        }
        catch (error) {
            console.log(chalk_1.default.red(`❌ Failed to change model: ${error instanceof Error ? error.message : String(error)}`));
            return {};
        }
    }
    async executeClear(_args) {
        const answer = await inquirer_1.default.prompt([
            {
                type: 'confirm',
                name: 'confirm',
                message: 'Are you sure you want to clear the chat history?',
                default: false
            }
        ]);
        if (answer.confirm) {
            console.log(chalk_1.default.green('✅ Chat history cleared'));
            return { shouldClear: true };
        }
        return {};
    }
    async executeExit(_args) {
        const answer = await inquirer_1.default.prompt([
            {
                type: 'confirm',
                name: 'confirm',
                message: 'Are you sure you want to exit?',
                default: true
            }
        ]);
        if (answer.confirm) {
            console.log(chalk_1.default.green('👋 Goodbye!'));
            return { shouldExit: true };
        }
        return {};
    }
    async executeStatus(_args, config) {
        if (!config) {
            console.log(chalk_1.default.red('❌ No configuration available'));
            return {};
        }
        console.log(chalk_1.default.cyan.bold('\n📊 Current Configuration\n'));
        console.log(chalk_1.default.white('Provider: ') + chalk_1.default.green(config.provider));
        console.log(chalk_1.default.white('Model: ') + chalk_1.default.green(config.model));
        if (config.baseUrl) {
            console.log(chalk_1.default.white('Base URL: ') + chalk_1.default.green(config.baseUrl));
        }
        console.log(chalk_1.default.white('Max Retries: ') + chalk_1.default.green(config.maxRetries.toString()));
        console.log(chalk_1.default.white('Timeout: ') + chalk_1.default.green(`${config.timeout}ms`));
        // Test connection
        console.log(chalk_1.default.blue('\n🔍 Testing connection...'));
        try {
            if (config.provider === 'deepseek') {
                const provider = new deepseek_1.DeepseekProvider(config.apiKey);
                await provider.sendMessage([
                    { role: 'user', content: 'test' }
                ], undefined, config.model);
                console.log(chalk_1.default.green('✅ Connection successful'));
            }
            else if (config.provider === 'ollama') {
                const provider = new ollama_1.OllamaProvider(config.baseUrl);
                const isConnected = await provider.checkConnection();
                if (isConnected) {
                    await provider.initialize();
                    console.log(chalk_1.default.green('✅ Connection successful'));
                    const models = provider.getAvailableModels();
                    console.log(chalk_1.default.white(`Available models: ${models.join(', ')}`));
                }
                else {
                    console.log(chalk_1.default.red('❌ Connection failed'));
                }
            }
        }
        catch (error) {
            console.log(chalk_1.default.red(`❌ Connection failed: ${error instanceof Error ? error.message : String(error)}`));
        }
        console.log();
        return {};
    }
    async executeTools(_args) {
        console.log(chalk_1.default.cyan.bold('\n🔧 Available Tools\n'));
        const tools = [
            { name: 'bash', description: 'Execute shell commands and system operations' },
            { name: 'grep', description: 'Search for text patterns in files' },
            { name: 'glob', description: 'Find files by name patterns' },
            { name: 'write', description: 'Create or overwrite files' },
            { name: 'edit', description: 'Edit existing files precisely' },
            { name: 'web', description: 'Search the internet for information' }
        ];
        for (const tool of tools) {
            console.log(chalk_1.default.white(`${tool.name}`) + chalk_1.default.gray(` - ${tool.description}`));
        }
        console.log(chalk_1.default.gray('\nThe AI will automatically choose and use these tools based on your requests.\n'));
        return {};
    }
    getAvailableCommands() {
        return Array.from(this.commands.keys());
    }
    getCommandDescription(commandName) {
        return this.commands.get(commandName)?.description;
    }
}
exports.SlashCommandsComponent = SlashCommandsComponent;
//# sourceMappingURL=slash-commands.js.map