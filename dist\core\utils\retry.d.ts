import { RetryOptions } from '../../types';
export declare class RetryManager {
    private activeOperations;
    executeWithRetry<T>(operation: () => Promise<T>, options: RetryOptions, onRetry?: (attempt: number, error: Error) => void): Promise<T>;
    private calculateDelay;
    private sleep;
    cancelAll(): void;
    getActiveOperationCount(): number;
}
export declare function createNetworkRetryOptions(): RetryOptions;
export declare function createRateLimitRetryOptions(): RetryOptions;
export declare function createFileOperationRetryOptions(): RetryOptions;
export declare class ErrorClassifier {
    static isNetworkError(error: Error): boolean;
    static isRateLimitError(error: Error): boolean;
    static isTemporaryError(error: Error): boolean;
    static isPermanentError(error: Error): boolean;
    static shouldRetry(error: Error): boolean;
    static getRetryDelay(error: Error, attempt: number): number;
}
//# sourceMappingURL=retry.d.ts.map