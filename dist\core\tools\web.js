"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebTool = exports.webToolDefinition = void 0;
const axios_1 = __importDefault(require("axios"));
exports.webToolDefinition = {
    type: 'function',
    function: {
        name: 'web',
        description: `Web search tool for retrieving and fetching real-time and up-to-date information automatically from the internet using free DuckDuckGo API.

WHEN TO USE:
- Getting current information and latest updates
- Checking documentation and API references
- Finding solutions to specific problems or errors
- Researching best practices and tutorials
- Verifying package versions and compatibility
- Looking up technical specifications

WHEN NOT TO USE:
- Local file operations or system information
- When offline information is sufficient
- For sensitive or private data searches
- When specific local tools are more appropriate

USAGE NOTES:
- Uses DuckDuckGo Instant Answer API for reliable results
- Returns structured information when available
- Respects rate limits and API guidelines
- Provides fallback search results if instant answers unavailable
- Filters results for relevance and quality
- Handles network timeouts and errors gracefully

SEARCH EXAMPLES:
- "latest Node.js version 2025" - Get current software versions
- "TypeScript 5.8 new features" - Learn about updates
- "npm install error EACCES fix" - Find solutions to problems
- "React hooks best practices" - Research development patterns
- "Docker compose syntax examples" - Get configuration help
- "Python pandas dataframe tutorial" - Find learning resources`,
        parameters: {
            type: 'object',
            properties: {
                query: {
                    type: 'string',
                    description: 'Search query to find information on the web'
                },
                maxResults: {
                    type: 'number',
                    description: 'Maximum number of results to return (optional, defaults to 5)'
                },
                timeout: {
                    type: 'number',
                    description: 'Request timeout in milliseconds (optional, defaults to 10000)'
                }
            },
            required: ['query']
        }
    }
};
class WebTool {
    baseUrl = 'https://api.duckduckgo.com/';
    userAgent = 'Arien-AI-CLI/1.0.0';
    async execute(options) {
        const startTime = Date.now();
        try {
            const { query, maxResults = 5, timeout = 10000 } = options;
            // Validate query
            if (!query || query.trim().length === 0) {
                return {
                    success: false,
                    output: '',
                    error: 'Search query cannot be empty'
                };
            }
            // Perform search
            const searchResults = await this.searchDuckDuckGo(query, timeout);
            // Format results
            const output = this.formatSearchResults(searchResults, query, maxResults);
            const executionTime = Date.now() - startTime;
            return {
                success: true,
                output,
                metadata: {
                    query,
                    resultsFound: searchResults.length,
                    maxResults,
                    executionTime,
                    searchEngine: 'DuckDuckGo'
                }
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Web search failed: ${error instanceof Error ? error.message : String(error)}`,
                metadata: {
                    query: options.query,
                    executionTime: Date.now() - startTime
                }
            };
        }
    }
    async searchDuckDuckGo(query, timeout) {
        try {
            // Use DuckDuckGo Instant Answer API
            const response = await axios_1.default.get(this.baseUrl, {
                params: {
                    q: query,
                    format: 'json',
                    no_html: '1',
                    skip_disambig: '1'
                },
                headers: {
                    'User-Agent': this.userAgent
                },
                timeout
            });
            const data = response.data;
            const results = [];
            // Extract instant answer if available
            if (data.Answer) {
                results.push({
                    title: 'Instant Answer',
                    url: data.AbstractURL || '',
                    snippet: data.Answer
                });
            }
            // Extract abstract if available
            if (data.Abstract && data.AbstractText) {
                results.push({
                    title: data.Heading || 'Abstract',
                    url: data.AbstractURL || '',
                    snippet: data.AbstractText
                });
            }
            // Extract definition if available
            if (data.Definition) {
                results.push({
                    title: 'Definition',
                    url: data.DefinitionURL || '',
                    snippet: data.Definition
                });
            }
            // Extract related topics
            if (data.RelatedTopics && Array.isArray(data.RelatedTopics)) {
                for (const topic of data.RelatedTopics.slice(0, 3)) {
                    if (topic.Text && topic.FirstURL) {
                        results.push({
                            title: this.extractTitleFromText(topic.Text),
                            url: topic.FirstURL,
                            snippet: topic.Text
                        });
                    }
                }
            }
            // Extract results
            if (data.Results && Array.isArray(data.Results)) {
                for (const result of data.Results.slice(0, 3)) {
                    if (result.Text && result.FirstURL) {
                        results.push({
                            title: this.extractTitleFromText(result.Text),
                            url: result.FirstURL,
                            snippet: result.Text
                        });
                    }
                }
            }
            // If no results from instant API, try a fallback search
            if (results.length === 0) {
                return await this.fallbackSearch(query, timeout);
            }
            return results;
        }
        catch (error) {
            // Try fallback search if main search fails
            if (axios_1.default.isAxiosError(error) && error.code !== 'ECONNABORTED') {
                return await this.fallbackSearch(query, timeout);
            }
            throw error;
        }
    }
    async fallbackSearch(query, _timeout) {
        // This is a simplified fallback - in a real implementation,
        // you might use other search APIs or scraping methods
        return [{
                title: 'Search Suggestion',
                url: `https://duckduckgo.com/?q=${encodeURIComponent(query)}`,
                snippet: `Search for "${query}" on DuckDuckGo for more detailed results. The instant answer API didn't return specific results for this query.`
            }];
    }
    extractTitleFromText(text) {
        // Extract title from text (usually the first part before a dash or description)
        const parts = text.split(' - ');
        if (parts.length > 1 && parts[0]) {
            return parts[0].trim();
        }
        // Try to extract from HTML-like text
        const match = text.match(/^([^<]+)/);
        if (match && match[1]) {
            return match[1].trim();
        }
        // Fallback: use first 50 characters
        return text.length > 50 ? text.substring(0, 50) + '...' : text;
    }
    formatSearchResults(results, query, maxResults) {
        if (results.length === 0) {
            return `No results found for query: "${query}"\n\nTry refining your search terms or checking for typos.`;
        }
        const output = [];
        output.push(`🔍 Search Results for: "${query}"\n`);
        const limitedResults = results.slice(0, maxResults);
        for (let i = 0; i < limitedResults.length; i++) {
            const result = limitedResults[i];
            if (result) {
                output.push(`${i + 1}. **${result.title}**`);
                if (result.url) {
                    output.push(`   🔗 ${result.url}`);
                }
                if (result.snippet) {
                    const snippet = result.snippet.length > 200
                        ? result.snippet.substring(0, 200) + '...'
                        : result.snippet;
                    output.push(`   📝 ${snippet}`);
                }
                output.push(''); // Empty line between results
            }
        }
        if (results.length > maxResults) {
            output.push(`... and ${results.length - maxResults} more results available`);
        }
        return output.join('\n');
    }
    // Utility method to validate search query
    validateQuery(query) {
        if (!query || query.trim().length === 0) {
            return { valid: false, error: 'Query cannot be empty' };
        }
        if (query.length > 500) {
            return { valid: false, error: 'Query too long (max 500 characters)' };
        }
        // Check for potentially problematic characters
        const problematicChars = /[<>{}[\]\\]/;
        if (problematicChars.test(query)) {
            return { valid: false, error: 'Query contains potentially problematic characters' };
        }
        return { valid: true };
    }
    // Utility method to suggest search improvements
    suggestQueryImprovements(query) {
        const suggestions = [];
        if (query.length < 3) {
            suggestions.push('Try using more specific search terms');
        }
        if (!query.includes(' ')) {
            suggestions.push('Consider adding more descriptive words');
        }
        if (query.toLowerCase() === query) {
            suggestions.push('Try including proper nouns or specific terms');
        }
        if (query.includes('how to')) {
            suggestions.push('Consider adding specific technology or context');
        }
        return suggestions;
    }
}
exports.WebTool = WebTool;
//# sourceMappingURL=web.js.map