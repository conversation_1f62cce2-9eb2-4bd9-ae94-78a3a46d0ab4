import { ToolDefinition, ToolResult, GlobToolOptions } from '../../types';
export declare const globToolDefinition: ToolDefinition;
export declare class GlobTool {
    private readonly defaultIgnorePatterns;
    execute(options: GlobToolOptions): Promise<ToolResult>;
    private formatResults;
    private formatFileSize;
    validatePattern(pattern: string): {
        valid: boolean;
        error?: string;
    };
    suggestPatterns(intent: string): string[];
}
//# sourceMappingURL=glob.d.ts.map