"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.configManager = exports.ConfigManager = void 0;
const path_1 = __importDefault(require("path"));
const os_1 = __importDefault(require("os"));
// Use require for conf to avoid ES module issues
const Conf = require('conf').default || require('conf');
class ConfigManager {
    config;
    constructor() {
        this.config = new Conf({
            projectName: 'arien-ai-cli',
            projectVersion: '1.0.0',
            defaults: {
                provider: 'deepseek',
                model: 'deepseek-chat',
                maxRetries: 3,
                retryDelay: 1000,
                timeout: 30000
            },
            schema: {
                provider: {
                    type: 'string',
                    enum: ['deepseek', 'ollama']
                },
                apiKey: {
                    type: 'string'
                },
                model: {
                    type: 'string'
                },
                baseUrl: {
                    type: 'string'
                },
                maxRetries: {
                    type: 'number',
                    minimum: 0,
                    maximum: 10
                },
                retryDelay: {
                    type: 'number',
                    minimum: 100,
                    maximum: 10000
                },
                timeout: {
                    type: 'number',
                    minimum: 5000,
                    maximum: 120000
                }
            }
        });
    }
    get() {
        return this.config.store;
    }
    set(newConfig) {
        // Merge with existing config
        const currentConfig = this.get();
        const mergedConfig = { ...currentConfig, ...newConfig };
        // Validate the merged config
        this.validateConfig(mergedConfig);
        // Store the config
        this.config.store = mergedConfig;
    }
    update(updates) {
        for (const [key, value] of Object.entries(updates)) {
            if (value !== undefined) {
                this.config.set(key, value);
            }
        }
    }
    reset() {
        this.config.clear();
    }
    exists() {
        return this.config.size > 0;
    }
    getConfigPath() {
        return this.config.path;
    }
    validateConfig(config) {
        // Validate provider-specific requirements
        if (config.provider === 'deepseek') {
            if (!config.apiKey || config.apiKey.trim().length === 0) {
                throw new Error('API key is required for Deepseek provider');
            }
            const validModels = ['deepseek-chat', 'deepseek-reasoner'];
            if (!validModels.includes(config.model)) {
                throw new Error(`Invalid model for Deepseek: ${config.model}`);
            }
        }
        if (config.provider === 'ollama') {
            if (!config.baseUrl || config.baseUrl.trim().length === 0) {
                throw new Error('Base URL is required for Ollama provider');
            }
            try {
                new URL(config.baseUrl);
            }
            catch {
                throw new Error('Invalid base URL for Ollama provider');
            }
        }
        // Validate numeric ranges
        if (config.maxRetries < 0 || config.maxRetries > 10) {
            throw new Error('Max retries must be between 0 and 10');
        }
        if (config.retryDelay < 100 || config.retryDelay > 10000) {
            throw new Error('Retry delay must be between 100ms and 10s');
        }
        if (config.timeout < 5000 || config.timeout > 120000) {
            throw new Error('Timeout must be between 5s and 120s');
        }
    }
    // Export configuration for backup
    export() {
        const config = this.get();
        // Remove sensitive data for export
        const exportConfig = { ...config };
        if (exportConfig.apiKey) {
            exportConfig.apiKey = '***REDACTED***';
        }
        return JSON.stringify(exportConfig, null, 2);
    }
    // Import configuration from backup
    import(configJson) {
        try {
            const importedConfig = JSON.parse(configJson);
            // Don't import redacted API keys
            if (importedConfig.apiKey === '***REDACTED***') {
                delete importedConfig.apiKey;
            }
            this.set(importedConfig);
        }
        catch (error) {
            throw new Error(`Failed to import configuration: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    // Get default configuration for a provider
    static getDefaultConfig(provider) {
        const base = {
            maxRetries: 3,
            retryDelay: 1000,
            timeout: 30000
        };
        if (provider === 'deepseek') {
            return {
                ...base,
                provider: 'deepseek',
                model: 'deepseek-chat'
            };
        }
        else {
            return {
                ...base,
                provider: 'ollama',
                baseUrl: 'http://localhost:11434',
                model: 'llama3.2',
                timeout: 60000 // Longer timeout for local models
            };
        }
    }
    // Migration utilities
    migrate() {
        const currentVersion = this.config.get('version');
        if (!currentVersion) {
            // First time setup or very old version
            this.config.set('version', '1.0.0');
        }
        // Add future migration logic here
    }
    // Utility to get user data directory
    static getUserDataDir() {
        const platform = os_1.default.platform();
        switch (platform) {
            case 'win32':
                return path_1.default.join(os_1.default.homedir(), 'AppData', 'Roaming', 'arien-ai-cli');
            case 'darwin':
                return path_1.default.join(os_1.default.homedir(), 'Library', 'Application Support', 'arien-ai-cli');
            default:
                return path_1.default.join(os_1.default.homedir(), '.config', 'arien-ai-cli');
        }
    }
    // Utility to check if config is valid
    isValid() {
        try {
            const config = this.get();
            this.validateConfig(config);
            return { valid: true, errors: [] };
        }
        catch (error) {
            return {
                valid: false,
                errors: [error instanceof Error ? error.message : String(error)]
            };
        }
    }
}
exports.ConfigManager = ConfigManager;
// Singleton instance
exports.configManager = new ConfigManager();
//# sourceMappingURL=index.js.map