import { execa, ExecaError } from 'execa';
import { ToolDefinition, ToolResult, BashToolOptions } from '@/types';
import path from 'path';
import os from 'os';

export const bashToolDefinition: ToolDefinition = {
  type: 'function',
  function: {
    name: 'bash',
    description: `Execute bash/shell commands in an interactive environment. This tool runs and executes all types of bash commands in the interactive shell.

WHEN TO USE:
- System operations (file management, process control)
- Running programs and applications
- Installing software packages (npm, pip, apt, etc.)
- Git operations (clone, commit, push, pull)
- Build and compilation tasks
- Environment setup and configuration
- Network operations and testing

WHEN NOT TO USE:
- Simple file reading (use grep tool instead)
- File content writing (use write tool instead)
- File pattern searching (use glob tool instead)

USAGE NOTES:
- Commands execute in the specified working directory
- Environment variables can be set for the command
- Long-running commands have configurable timeout
- Interactive commands may require special handling
- Always handle errors gracefully and provide alternatives
- Use absolute paths when possible for reliability

EXAMPLES:
- "npm install express typescript" - Install Node.js packages
- "git clone https://github.com/user/repo.git" - Clone repository
- "mkdir -p src/components && cd src" - Create directories and navigate
- "ls -la | grep .json" - List and filter files
- "node --version && npm --version" - Check versions
- "chmod +x script.sh && ./script.sh" - Make executable and run`,
    parameters: {
      type: 'object',
      properties: {
        command: {
          type: 'string',
          description: 'The bash command to execute'
        },
        workingDirectory: {
          type: 'string',
          description: 'Working directory for command execution (optional, defaults to current directory)'
        },
        timeout: {
          type: 'number',
          description: 'Timeout in milliseconds (optional, defaults to 30000)'
        },
        env: {
          type: 'object',
          description: 'Environment variables to set for the command (optional)',
          additionalProperties: {
            type: 'string'
          }
        }
      },
      required: ['command']
    }
  }
};

export class BashTool {
  async execute(options: BashToolOptions): Promise<ToolResult> {
    const startTime = Date.now();
    
    try {
      const {
        command,
        workingDirectory = process.cwd(),
        timeout = 30000,
        env = {}
      } = options;

      // Validate inputs
      if (!command || command.trim().length === 0) {
        return {
          success: false,
          output: '',
          error: 'Command cannot be empty'
        };
      }

      // Resolve working directory
      const resolvedCwd = path.resolve(workingDirectory);

      // Prepare environment variables
      const execEnv = {
        ...process.env,
        ...env
      };

      // Determine shell based on platform
      const shell = os.platform() === 'win32' ? 'powershell.exe' : '/bin/bash';
      const shellArgs = os.platform() === 'win32' ? ['-Command'] : ['-c'];

      // Execute command
      const result = await execa(shell, [...shellArgs, command], {
        cwd: resolvedCwd,
        env: execEnv,
        timeout,
        encoding: 'utf8',
        stripFinalNewline: false,
        reject: false // Don't throw on non-zero exit codes
      });

      const executionTime = Date.now() - startTime;

      // Prepare output
      const output = [
        result.stdout && result.stdout.trim() ? `STDOUT:\n${result.stdout}` : '',
        result.stderr && result.stderr.trim() ? `STDERR:\n${result.stderr}` : ''
      ].filter(Boolean).join('\n\n');

      const success = result.exitCode === 0;

      return {
        success,
        output: output || (success ? 'Command executed successfully (no output)' : ''),
        error: success ? undefined : `Command failed with exit code ${result.exitCode}`,
        metadata: {
          exitCode: result.exitCode,
          executionTime,
          command,
          workingDirectory: resolvedCwd,
          pid: (result as any).pid
        }
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;

      if (error instanceof ExecaError) {
        return {
          success: false,
          output: error.stdout || '',
          error: this.formatExecaError(error),
          metadata: {
            exitCode: error.exitCode,
            executionTime,
            command: options.command,
            workingDirectory: options.workingDirectory || process.cwd(),
            signal: error.signal,
            timedOut: error.timedOut
          }
        };
      }

      return {
        success: false,
        output: '',
        error: `Unexpected error: ${error instanceof Error ? error.message : String(error)}`,
        metadata: {
          executionTime,
          command: options.command,
          workingDirectory: options.workingDirectory || process.cwd()
        }
      };
    }
  }

  private formatExecaError(error: ExecaError): string {
    const parts: string[] = [];

    if (error.timedOut) {
      parts.push('Command timed out');
    }

    if (error.signal) {
      parts.push(`Killed by signal: ${error.signal}`);
    }

    if (error.exitCode !== undefined) {
      parts.push(`Exit code: ${error.exitCode}`);
    }

    if (error.stderr) {
      parts.push(`Error output: ${error.stderr}`);
    }

    if (error.message && !parts.some(part => part.includes(error.message))) {
      parts.push(`Message: ${error.message}`);
    }

    return parts.join('; ') || 'Command execution failed';
  }

  // Utility method to check if a command exists
  async commandExists(command: string): Promise<boolean> {
    try {
      const checkCommand = os.platform() === 'win32' 
        ? `Get-Command ${command} -ErrorAction SilentlyContinue`
        : `command -v ${command}`;

      const result = await this.execute({
        command: checkCommand,
        timeout: 5000
      });

      return result.success;
    } catch {
      return false;
    }
  }

  // Utility method to get current working directory
  async getCurrentDirectory(): Promise<string> {
    try {
      const result = await this.execute({
        command: os.platform() === 'win32' ? 'pwd' : 'pwd',
        timeout: 5000
      });

      if (result.success && result.output) {
        return result.output.replace('STDOUT:\n', '').trim();
      }
    } catch {
      // Fallback to process.cwd()
    }

    return process.cwd();
  }
}
