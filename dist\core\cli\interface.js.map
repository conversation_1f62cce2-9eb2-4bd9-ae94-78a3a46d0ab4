{"version": 3, "file": "interface.js", "sourceRoot": "", "sources": ["../../../src/core/cli/interface.ts"], "names": [], "mappings": ";;;;;;AACA,8CAAoD;AACpD,4CAAkD;AAClD,gEAAqE;AACrE,2CAA0C;AAC1C,qCAAyC;AACzC,kDAA0B;AAC1B,mCAAoC;AAEpC,MAAa,YAAY;IACf,cAAc,CAAiB;IAC/B,aAAa,CAAgB;IAC7B,aAAa,CAAyB;IACtC,OAAO,CAAU;IACjB,OAAO,GAA0B,IAAI,CAAC;IAE9C;QACE,IAAI,CAAC,cAAc,GAAG,IAAI,sBAAc,EAAE,CAAC;QAC3C,IAAI,CAAC,aAAa,GAAG,IAAI,oBAAa,EAAE,CAAC;QACzC,IAAI,CAAC,aAAa,GAAG,IAAI,uCAAsB,EAAE,CAAC;QAClD,IAAI,CAAC,OAAO,GAAG,IAAI,eAAO,EAAE,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC;YACH,OAAO,CAAC,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,aAAa,EAAE,CAAC;YAErB,2BAA2B;YAC3B,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAErC,sBAAsB;YACtB,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAQ,CAAC,CAAC;YAE7C,0BAA0B;YAC1B,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;YAErC,4BAA4B;YAC5B,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAErC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,0BAA0B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3G,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAEO,aAAa;QACnB,MAAM,MAAM,GAAG;;;;;;;;;;;;;;KAcd,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IAClC,CAAC;IAEO,KAAK,CAAC,uBAAuB;QACnC,IAAI,MAAiB,CAAC;QAEtB,gCAAgC;QAChC,IAAI,sBAAa,CAAC,MAAM,EAAE,EAAE,CAAC;YAC3B,MAAM,GAAG,sBAAa,CAAC,GAAG,EAAE,CAAC;YAE7B,kCAAkC;YAClC,MAAM,UAAU,GAAG,sBAAa,CAAC,OAAO,EAAE,CAAC;YAC3C,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,iCAAiC,CAAC,CAAC,CAAC;gBAC7D,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC5E,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC;gBAEtD,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;gBAC/C,sBAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,mCAAmC,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC;YACpG,CAAC;QACH,CAAC;aAAM,CAAC;YACN,mBAAmB;YACnB,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;YAC/C,sBAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC5B,CAAC;QAED,yBAAyB;QACzB,IAAI,CAAC,OAAO,GAAG;YACb,MAAM;YACN,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE;YAChC,gBAAgB,EAAE,OAAO,CAAC,GAAG,EAAE;YAC/B,aAAa,EAAE,KAAK;SACrB,CAAC;IACJ,CAAC;IAEO,gBAAgB;QACtB,OAAO;YACL,EAAE,EAAE,IAAA,mBAAU,GAAE;YAChB,QAAQ,EAAE,EAAE;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,qBAAqB;QACjC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC,CAAC;QAEzF,OAAO,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC;gBACH,wBAAwB;gBACxB,IAAI,CAAC,aAAa,CAAC,mBAAmB,EAAE,CAAC;gBAEzC,uBAAuB;gBACvB,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAQ,CAAC,CAAC;gBAErD,iBAAiB;gBACjB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;gBAElD,uBAAuB;gBACvB,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC9B,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;oBACxC,MAAM;gBACR,CAAC;gBAED,mDAAmD;gBACnD,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC1B,IAAI,YAAY,GAAG,KAAK,CAAC;oBAEzB,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,GAAG,EAAE,CAAC;wBACzB,sBAAsB;wBACtB,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB,EAAE,CAAC;wBACrE,IAAI,eAAe,EAAE,CAAC;4BACpB,YAAY,GAAG,IAAI,eAAe,EAAE,CAAC;wBACvC,CAAC;6BAAM,CAAC;4BACN,SAAS,CAAC,iBAAiB;wBAC7B,CAAC;oBACH,CAAC;oBAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,YAAY,EAAE,IAAI,CAAC,OAAQ,CAAC,MAAM,CAAC,CAAC;oBAE/F,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;wBACtB,MAAM;oBACR,CAAC;oBAED,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;wBACvB,IAAI,CAAC,OAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBAChD,OAAO,CAAC,KAAK,EAAE,CAAC;wBAChB,IAAI,CAAC,aAAa,EAAE,CAAC;wBACrB,SAAS;oBACX,CAAC;oBAED,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;wBACrB,IAAI,CAAC,OAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC;wBACxC,sBAAa,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;wBACpC,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAQ,CAAC,CAAC;oBAC/C,CAAC;oBAED,SAAS;gBACX,CAAC;gBAED,sBAAsB;gBACtB,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE,CAAC;oBACnC,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;oBACjC,SAAS;gBACX,CAAC;gBAED,0BAA0B;gBAC1B,MAAM,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,OAAQ,CAAC,CAAC;gBAExD,2BAA2B;gBAC3B,IAAI,CAAC,OAAQ,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;gBAE7C,gCAAgC;gBAChC,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;YAExC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;oBACpE,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,0BAA0B,CAAC,CAAC,CAAC;gBACxD,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,aAAa,CAAC,YAAY,CAC7B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EACtD,qBAAqB,CACtB,CAAC;gBACJ,CAAC;gBAED,6BAA6B;gBAC7B,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;YACxC,CAAC;QACH,CAAC;QAED,UAAU;QACV,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;IACxB,CAAC;IAEO,aAAa,CAAC,KAAa;QACjC,MAAM,YAAY,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;QACxD,OAAO,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3D,CAAC;IAEO,KAAK,CAAC,QAAQ;QACpB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YAC9B,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,yBAAyB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;QAC/G,CAAC;IACH,CAAC;IAED,gDAAgD;IAChD,qBAAqB;QACnB,MAAM,cAAc,GAAG,KAAK,EAAE,MAAc,EAAE,EAAE;YAC9C,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,iBAAiB,MAAM,+BAA+B,CAAC,CAAC,CAAC;YAClF,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;YACtB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC;QAEF,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;QACrD,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC,CAAC;QAEvD,6BAA6B;QAC7B,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE;YACxC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,0BAA0B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAClE,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YACrC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,EAAE;YAC1C,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,GAAG,CAAC,2BAA2B,MAAM,EAAE,CAAC,CAAC,CAAC;YAC5D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,wCAAwC;IACxC,MAAM,CAAC,KAAK,CAAC,GAAG;QACd,MAAM,GAAG,GAAG,IAAI,YAAY,EAAE,CAAC;QAC/B,GAAG,CAAC,qBAAqB,EAAE,CAAC;QAC5B,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC;CACF;AA1OD,oCA0OC"}