import axios, { AxiosResponse } from 'axios';
import { AIProvider, AIMessage, ToolCall, ToolDefinition } from '@/types';

export interface OllamaResponse {
  model: string;
  created_at: string;
  message: {
    role: string;
    content: string;
    tool_calls?: Array<{
      function: {
        name: string;
        arguments: Record<string, any>;
      };
    }>;
  };
  done: boolean;
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

export interface OllamaModel {
  name: string;
  model: string;
  modified_at: string;
  size: number;
  digest: string;
  details: {
    parent_model: string;
    format: string;
    family: string;
    families: string[];
    parameter_size: string;
    quantization_level: string;
  };
}

export class OllamaProvider implements AIProvider {
  public readonly name = 'ollama';
  public readonly models: string[] = [];
  public defaultModel = 'llama3.2';

  constructor(
    public readonly baseUrl: string = 'http://localhost:11434',
    public readonly apiKey: string = '' // Ollama doesn't require API key by default
  ) {}

  async initialize(): Promise<void> {
    try {
      await this.loadAvailableModels();
    } catch (error) {
      throw new Error(`Failed to connect to Ollama at ${this.baseUrl}. Make sure Ollama is running.`);
    }
  }

  async loadAvailableModels(): Promise<void> {
    try {
      const response: AxiosResponse<{ models: OllamaModel[] }> = await axios.get(
        `${this.baseUrl}/api/tags`,
        { timeout: 5000 }
      );

      this.models.length = 0; // Clear existing models
      this.models.push(...response.data.models.map(model => model.name));

      if (this.models.length > 0 && !this.models.includes(this.defaultModel)) {
        this.defaultModel = this.models[0] || 'llama3.2';
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.code === 'ECONNREFUSED') {
          throw new Error('Cannot connect to Ollama. Please ensure Ollama is running on ' + this.baseUrl);
        }
        throw new Error(`Ollama connection error: ${error.message}`);
      }
      throw error;
    }
  }

  async sendMessage(
    messages: AIMessage[],
    tools?: ToolDefinition[],
    model: string = this.defaultModel,
    options: {
      temperature?: number;
      maxTokens?: number;
      stream?: boolean;
    } = {}
  ): Promise<AIMessage> {
    try {
      const requestBody = {
        model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content,
          tool_calls: msg.toolCalls
        })),
        tools: tools?.map(tool => tool.function),
        options: {
          temperature: options.temperature ?? 0.7,
          num_predict: options.maxTokens ?? 4000
        },
        stream: false
      };

      const response: AxiosResponse<OllamaResponse> = await axios.post(
        `${this.baseUrl}/api/chat`,
        requestBody,
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 60000
        }
      );

      const message: AIMessage = {
        role: 'assistant',
        content: response.data.message.content,
        toolCalls: response.data.message.tool_calls?.map(tc => ({
          id: `call_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          type: 'function' as const,
          function: {
            name: tc.function.name,
            arguments: JSON.stringify(tc.function.arguments)
          }
        }))
      };

      return message;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const status = error.response?.status;
        const message = error.response?.data?.error || error.message;
        
        if (status === 404) {
          throw new Error(`Model '${model}' not found. Available models: ${this.models.join(', ')}`);
        } else if (error.code === 'ECONNREFUSED') {
          throw new Error('Cannot connect to Ollama. Please ensure Ollama is running.');
        } else {
          throw new Error(`Ollama API error: ${message}`);
        }
      }
      throw error;
    }
  }

  async streamMessage(
    messages: AIMessage[],
    tools?: ToolDefinition[],
    model: string = this.defaultModel,
    onChunk?: (chunk: string) => void
  ): Promise<AIMessage> {
    try {
      const requestBody = {
        model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content,
          tool_calls: msg.toolCalls
        })),
        tools: tools?.map(tool => tool.function),
        options: {
          temperature: 0.7,
          num_predict: 4000
        },
        stream: true
      };

      const response = await axios.post(
        `${this.baseUrl}/api/chat`,
        requestBody,
        {
          headers: {
            'Content-Type': 'application/json'
          },
          responseType: 'stream',
          timeout: 120000
        }
      );

      let fullContent = '';
      let toolCalls: ToolCall[] = [];

      return new Promise((resolve, reject) => {
        response.data.on('data', (chunk: Buffer) => {
          const lines = chunk.toString().split('\n').filter(line => line.trim());
          
          for (const line of lines) {
            try {
              const parsed: OllamaResponse = JSON.parse(line);
              
              if (parsed.message?.content) {
                fullContent += parsed.message.content;
                onChunk?.(parsed.message.content);
              }

              if (parsed.message?.tool_calls) {
                for (const tc of parsed.message.tool_calls) {
                  toolCalls.push({
                    id: `call_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                    type: 'function',
                    function: {
                      name: tc.function.name,
                      arguments: JSON.stringify(tc.function.arguments)
                    }
                  });
                }
              }

              if (parsed.done) {
                resolve({
                  role: 'assistant',
                  content: fullContent,
                  toolCalls: toolCalls.length > 0 ? toolCalls : undefined
                });
                return;
              }
            } catch (parseError) {
              // Ignore parsing errors for incomplete chunks
            }
          }
        });

        response.data.on('error', (error: Error) => {
          reject(error);
        });

        response.data.on('end', () => {
          resolve({
            role: 'assistant',
            content: fullContent,
            toolCalls: toolCalls.length > 0 ? toolCalls : undefined
          });
        });
      });
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const message = error.response?.data?.error || error.message;
        throw new Error(`Ollama streaming error: ${message}`);
      }
      throw error;
    }
  }

  async pullModel(modelName: string): Promise<void> {
    try {
      await axios.post(
        `${this.baseUrl}/api/pull`,
        { name: modelName },
        {
          headers: { 'Content-Type': 'application/json' },
          timeout: 300000 // 5 minutes for model download
        }
      );

      // Refresh available models after pulling
      await this.loadAvailableModels();
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(`Failed to pull model '${modelName}': ${error.message}`);
      }
      throw error;
    }
  }

  validateModel(model: string): boolean {
    return this.models.includes(model);
  }

  getAvailableModels(): string[] {
    return [...this.models];
  }

  async checkConnection(): Promise<boolean> {
    try {
      await axios.get(`${this.baseUrl}/api/tags`, { timeout: 3000 });
      return true;
    } catch {
      return false;
    }
  }
}
