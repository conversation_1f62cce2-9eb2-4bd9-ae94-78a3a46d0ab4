import { CLIConfig } from '../types';
export declare class ConfigManager {
    private config;
    constructor();
    get(): CLIConfig;
    set(newConfig: Partial<CLIConfig>): void;
    update(updates: Partial<CLIConfig>): void;
    reset(): void;
    exists(): boolean;
    getConfigPath(): string;
    private validateConfig;
    export(): string;
    import(configJson: string): void;
    static getDefaultConfig(provider: 'deepseek' | 'ollama'): Partial<CLIConfig>;
    migrate(): void;
    static getUserDataDir(): string;
    isValid(): {
        valid: boolean;
        errors: string[];
    };
}
export declare const configManager: ConfigManager;
//# sourceMappingURL=index.d.ts.map