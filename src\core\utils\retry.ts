import { RetryOptions } from '@/types';

export class RetryManager {
  private activeOperations: Set<Promise<any>> = new Set();

  async executeWithRetry<T>(
    operation: () => Promise<T>,
    options: RetryOptions,
    onRetry?: (attempt: number, error: Error) => void
  ): Promise<T> {
    const {
      maxRetries,
      baseDelay,
      maxDelay,
      exponentialBackoff,
      retryCondition
    } = options;

    let lastError: Error;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const promise = operation();
        this.activeOperations.add(promise);
        
        const result = await promise;
        this.activeOperations.delete(promise);
        
        return result;
      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        lastError = err;
        
        // Don't retry on the last attempt
        if (attempt === maxRetries) {
          break;
        }

        // Check if we should retry this error
        if (retryCondition && !retryCondition(err)) {
          break;
        }

        // Call retry callback
        if (onRetry) {
          onRetry(attempt + 1, err);
        }

        // Calculate delay
        const delay = this.calculateDelay(attempt, baseDelay, maxDelay, exponentialBackoff);
        
        // Wait before retrying
        await this.sleep(delay);
      }
    }

    throw lastError!;
  }

  private calculateDelay(
    attempt: number, 
    baseDelay: number, 
    maxDelay: number, 
    exponentialBackoff: boolean
  ): number {
    if (!exponentialBackoff) {
      return Math.min(baseDelay, maxDelay);
    }

    // Exponential backoff with jitter
    const exponentialDelay = baseDelay * Math.pow(2, attempt);
    const jitter = Math.random() * 0.1 * exponentialDelay; // 10% jitter
    
    return Math.min(exponentialDelay + jitter, maxDelay);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  cancelAll(): void {
    // Note: This is a simplified cancellation mechanism
    // In a real implementation, you might want to use AbortController
    this.activeOperations.clear();
  }

  getActiveOperationCount(): number {
    return this.activeOperations.size;
  }
}

// Utility functions for common retry scenarios
export function createNetworkRetryOptions(): RetryOptions {
  return {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    exponentialBackoff: true,
    retryCondition: (error: Error) => {
      const networkErrors = [
        'ECONNRESET',
        'ENOTFOUND', 
        'ECONNREFUSED',
        'ETIMEDOUT',
        'timeout',
        'network'
      ];
      
      const errorMessage = error.message.toLowerCase();
      return networkErrors.some(netError => errorMessage.includes(netError));
    }
  };
}

export function createRateLimitRetryOptions(): RetryOptions {
  return {
    maxRetries: 5,
    baseDelay: 2000,
    maxDelay: 30000,
    exponentialBackoff: true,
    retryCondition: (error: Error) => {
      const rateLimitErrors = [
        'rate limit',
        'too many requests',
        '429',
        'quota exceeded'
      ];
      
      const errorMessage = error.message.toLowerCase();
      return rateLimitErrors.some(rlError => errorMessage.includes(rlError));
    }
  };
}

export function createFileOperationRetryOptions(): RetryOptions {
  return {
    maxRetries: 2,
    baseDelay: 500,
    maxDelay: 2000,
    exponentialBackoff: false,
    retryCondition: (error: Error) => {
      const fileErrors = [
        'EBUSY',
        'EMFILE',
        'ENFILE',
        'temporarily unavailable'
      ];
      
      const errorMessage = error.message.toLowerCase();
      return fileErrors.some(fileError => errorMessage.includes(fileError));
    }
  };
}

// Error classification utility
export class ErrorClassifier {
  static isNetworkError(error: Error): boolean {
    const networkIndicators = [
      'ECONNRESET',
      'ENOTFOUND',
      'ECONNREFUSED', 
      'ETIMEDOUT',
      'network',
      'timeout',
      'dns'
    ];
    
    const message = error.message.toLowerCase();
    return networkIndicators.some(indicator => message.includes(indicator));
  }

  static isRateLimitError(error: Error): boolean {
    const rateLimitIndicators = [
      'rate limit',
      'too many requests',
      '429',
      'quota exceeded',
      'throttled'
    ];
    
    const message = error.message.toLowerCase();
    return rateLimitIndicators.some(indicator => message.includes(indicator));
  }

  static isTemporaryError(error: Error): boolean {
    const temporaryIndicators = [
      'temporary',
      'temporarily',
      'try again',
      'retry',
      'busy',
      'unavailable',
      '503',
      '502',
      '504'
    ];
    
    const message = error.message.toLowerCase();
    return temporaryIndicators.some(indicator => message.includes(indicator));
  }

  static isPermanentError(error: Error): boolean {
    const permanentIndicators = [
      'unauthorized',
      'forbidden',
      'not found',
      'invalid',
      'malformed',
      'syntax error',
      '401',
      '403',
      '404',
      '400'
    ];
    
    const message = error.message.toLowerCase();
    return permanentIndicators.some(indicator => message.includes(indicator));
  }

  static shouldRetry(error: Error): boolean {
    // Don't retry permanent errors
    if (this.isPermanentError(error)) {
      return false;
    }
    
    // Retry network, rate limit, and temporary errors
    return this.isNetworkError(error) || 
           this.isRateLimitError(error) || 
           this.isTemporaryError(error);
  }

  static getRetryDelay(error: Error, attempt: number): number {
    if (this.isRateLimitError(error)) {
      // Longer delays for rate limits
      return Math.min(2000 * Math.pow(2, attempt), 30000);
    }
    
    if (this.isNetworkError(error)) {
      // Medium delays for network errors
      return Math.min(1000 * Math.pow(1.5, attempt), 10000);
    }
    
    // Short delays for other temporary errors
    return Math.min(500 * Math.pow(1.2, attempt), 5000);
  }
}
