"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EditTool = exports.editToolDefinition = void 0;
const fs_1 = require("fs");
const path_1 = __importDefault(require("path"));
exports.editToolDefinition = {
    type: 'function',
    function: {
        name: 'edit',
        description: `Edits files by replacing text, creating new files, or deleting content. For moving or renaming files, use the Bash tool with the 'mv' command instead. For larger file edits, use the File Write tool to overwrite files.

WHEN TO USE:
- Making precise edits to existing files
- Replacing specific text or code sections
- Inserting new content at specific locations
- Deleting specific lines or sections
- Adding imports, exports, or configuration entries
- Modifying function implementations or class methods

WHEN NOT TO USE:
- Creating entirely new files (use write tool instead)
- Complete file replacement (use write tool instead)
- Moving or renaming files (use bash tool with 'mv')
- Binary file modifications

USAGE NOTES:
- Supports line-based operations with precise line numbers
- Can search and replace using text patterns
- Maintains file encoding and permissions
- Creates backup before destructive operations
- Handles cross-platform line endings automatically
- Atomic operations to prevent file corruption

OPERATION TYPES:
- "replace": Replace specific text with new content
- "insert": Insert new content at specified line
- "delete": Remove specific lines or text sections
- "create": Create new file (alternative to write tool)

EXAMPLES:
- Replace function implementation in TypeScript file
- Add import statement at top of JavaScript file
- Insert configuration block in JSON file
- Delete deprecated code sections
- Modify package.json scripts or dependencies`,
        parameters: {
            type: 'object',
            properties: {
                path: {
                    type: 'string',
                    description: 'File path to edit'
                },
                operation: {
                    type: 'string',
                    enum: ['replace', 'insert', 'delete', 'create'],
                    description: 'Type of edit operation to perform'
                },
                content: {
                    type: 'string',
                    description: 'New content for replace/insert/create operations (optional for delete)'
                },
                lineNumber: {
                    type: 'number',
                    description: 'Line number for insert/delete operations (1-based, optional)'
                },
                searchPattern: {
                    type: 'string',
                    description: 'Text pattern to search for in replace/delete operations (optional)'
                },
                replaceWith: {
                    type: 'string',
                    description: 'Replacement text for replace operations (optional, defaults to content)'
                }
            },
            required: ['path', 'operation']
        }
    }
};
class EditTool {
    async execute(options) {
        const startTime = Date.now();
        try {
            const { path: filePath, operation } = options;
            // Validate inputs
            if (!filePath || filePath.trim().length === 0) {
                return {
                    success: false,
                    output: '',
                    error: 'File path cannot be empty'
                };
            }
            const resolvedPath = path_1.default.resolve(filePath);
            switch (operation) {
                case 'create':
                    return await this.createFile(resolvedPath, options, startTime);
                case 'replace':
                    return await this.replaceContent(resolvedPath, options, startTime);
                case 'insert':
                    return await this.insertContent(resolvedPath, options, startTime);
                case 'delete':
                    return await this.deleteContent(resolvedPath, options, startTime);
                default:
                    return {
                        success: false,
                        output: '',
                        error: `Unknown operation: ${operation}`,
                        metadata: { executionTime: Date.now() - startTime }
                    };
            }
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Edit operation failed: ${error instanceof Error ? error.message : String(error)}`,
                metadata: {
                    filePath: options.path,
                    operation: options.operation,
                    executionTime: Date.now() - startTime
                }
            };
        }
    }
    async createFile(filePath, options, startTime) {
        const { content = '' } = options;
        try {
            // Check if file already exists
            try {
                await fs_1.promises.access(filePath);
                return {
                    success: false,
                    output: '',
                    error: `File already exists: ${filePath}`,
                    metadata: { executionTime: Date.now() - startTime }
                };
            }
            catch {
                // File doesn't exist, which is what we want
            }
            // Create parent directories
            const directory = path_1.default.dirname(filePath);
            await fs_1.promises.mkdir(directory, { recursive: true });
            // Create file
            await fs_1.promises.writeFile(filePath, content, 'utf8');
            const stat = await fs_1.promises.stat(filePath);
            const relativePath = path_1.default.relative(process.cwd(), filePath);
            return {
                success: true,
                output: `Created file: ${relativePath} (${this.formatFileSize(stat.size)})`,
                metadata: {
                    filePath,
                    relativePath,
                    operation: 'create',
                    fileSize: stat.size,
                    linesCreated: content.split('\n').length,
                    executionTime: Date.now() - startTime
                }
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to create file: ${error instanceof Error ? error.message : String(error)}`,
                metadata: { executionTime: Date.now() - startTime }
            };
        }
    }
    async replaceContent(filePath, options, startTime) {
        const { searchPattern, replaceWith, content } = options;
        if (!searchPattern) {
            return {
                success: false,
                output: '',
                error: 'Search pattern is required for replace operation',
                metadata: { executionTime: Date.now() - startTime }
            };
        }
        const replacement = replaceWith ?? content ?? '';
        try {
            // Read file
            const originalContent = await fs_1.promises.readFile(filePath, 'utf8');
            // Perform replacement
            const newContent = originalContent.replace(new RegExp(searchPattern, 'g'), replacement);
            if (newContent === originalContent) {
                return {
                    success: true,
                    output: `No matches found for pattern: ${searchPattern}`,
                    metadata: {
                        filePath,
                        operation: 'replace',
                        searchPattern,
                        matchesFound: 0,
                        executionTime: Date.now() - startTime
                    }
                };
            }
            // Write updated content
            await fs_1.promises.writeFile(filePath, newContent, 'utf8');
            const matchCount = (originalContent.match(new RegExp(searchPattern, 'g')) || []).length;
            const relativePath = path_1.default.relative(process.cwd(), filePath);
            return {
                success: true,
                output: `Replaced ${matchCount} occurrence(s) in: ${relativePath}`,
                metadata: {
                    filePath,
                    relativePath,
                    operation: 'replace',
                    searchPattern,
                    replacement,
                    matchesFound: matchCount,
                    originalSize: originalContent.length,
                    newSize: newContent.length,
                    executionTime: Date.now() - startTime
                }
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to replace content: ${error instanceof Error ? error.message : String(error)}`,
                metadata: { executionTime: Date.now() - startTime }
            };
        }
    }
    async insertContent(filePath, options, startTime) {
        const { lineNumber, content = '' } = options;
        if (lineNumber === undefined) {
            return {
                success: false,
                output: '',
                error: 'Line number is required for insert operation',
                metadata: { executionTime: Date.now() - startTime }
            };
        }
        try {
            // Read file
            const originalContent = await fs_1.promises.readFile(filePath, 'utf8');
            const lines = originalContent.split('\n');
            // Validate line number
            if (lineNumber < 1 || lineNumber > lines.length + 1) {
                return {
                    success: false,
                    output: '',
                    error: `Invalid line number: ${lineNumber} (file has ${lines.length} lines)`,
                    metadata: { executionTime: Date.now() - startTime }
                };
            }
            // Insert content
            const insertIndex = lineNumber - 1;
            lines.splice(insertIndex, 0, content);
            // Write updated content
            const newContent = lines.join('\n');
            await fs_1.promises.writeFile(filePath, newContent, 'utf8');
            const relativePath = path_1.default.relative(process.cwd(), filePath);
            return {
                success: true,
                output: `Inserted content at line ${lineNumber} in: ${relativePath}`,
                metadata: {
                    filePath,
                    relativePath,
                    operation: 'insert',
                    lineNumber,
                    insertedContent: content,
                    originalLines: lines.length - 1,
                    newLines: lines.length,
                    executionTime: Date.now() - startTime
                }
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to insert content: ${error instanceof Error ? error.message : String(error)}`,
                metadata: { executionTime: Date.now() - startTime }
            };
        }
    }
    async deleteContent(filePath, options, startTime) {
        const { lineNumber, searchPattern } = options;
        if (!lineNumber && !searchPattern) {
            return {
                success: false,
                output: '',
                error: 'Either line number or search pattern is required for delete operation',
                metadata: { executionTime: Date.now() - startTime }
            };
        }
        try {
            // Read file
            const originalContent = await fs_1.promises.readFile(filePath, 'utf8');
            let newContent;
            let deletedCount = 0;
            if (lineNumber) {
                // Delete specific line
                const lines = originalContent.split('\n');
                if (lineNumber < 1 || lineNumber > lines.length) {
                    return {
                        success: false,
                        output: '',
                        error: `Invalid line number: ${lineNumber} (file has ${lines.length} lines)`,
                        metadata: { executionTime: Date.now() - startTime }
                    };
                }
                lines.splice(lineNumber - 1, 1);
                newContent = lines.join('\n');
                deletedCount = 1;
            }
            else if (searchPattern) {
                // Delete lines matching pattern
                const lines = originalContent.split('\n');
                const regex = new RegExp(searchPattern);
                const filteredLines = lines.filter(line => !regex.test(line));
                deletedCount = lines.length - filteredLines.length;
                newContent = filteredLines.join('\n');
            }
            else {
                throw new Error('No deletion criteria specified');
            }
            if (deletedCount === 0) {
                return {
                    success: true,
                    output: searchPattern
                        ? `No lines found matching pattern: ${searchPattern}`
                        : 'No content deleted',
                    metadata: {
                        filePath,
                        operation: 'delete',
                        deletedCount: 0,
                        executionTime: Date.now() - startTime
                    }
                };
            }
            // Write updated content
            await fs_1.promises.writeFile(filePath, newContent, 'utf8');
            const relativePath = path_1.default.relative(process.cwd(), filePath);
            const description = lineNumber
                ? `line ${lineNumber}`
                : `${deletedCount} line(s) matching pattern`;
            return {
                success: true,
                output: `Deleted ${description} from: ${relativePath}`,
                metadata: {
                    filePath,
                    relativePath,
                    operation: 'delete',
                    lineNumber,
                    searchPattern,
                    deletedCount,
                    originalSize: originalContent.length,
                    newSize: newContent.length,
                    executionTime: Date.now() - startTime
                }
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Failed to delete content: ${error instanceof Error ? error.message : String(error)}`,
                metadata: { executionTime: Date.now() - startTime }
            };
        }
    }
    formatFileSize(bytes) {
        if (bytes === 0)
            return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
}
exports.EditTool = EditTool;
//# sourceMappingURL=edit.js.map