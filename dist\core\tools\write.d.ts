import { ToolDefinition, ToolResult, WriteToolOptions } from '../../types';
export declare const writeToolDefinition: ToolDefinition;
export declare class WriteTool {
    execute(options: WriteToolOptions): Promise<ToolResult>;
    private isDangerousPath;
    private formatFileSize;
    backupFile(filePath: string): Promise<string | null>;
    validateContent(content: string, options?: {
        maxSize?: number;
        allowEmpty?: boolean;
    }): {
        valid: boolean;
        error?: string;
    };
    suggestExtension(content: string): string;
}
//# sourceMappingURL=write.d.ts.map