"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatComponent = void 0;
const inquirer_1 = __importDefault(require("inquirer"));
const chalk_1 = __importDefault(require("chalk"));
const spinner_1 = require("./spinner");
class ChatComponent {
    isInterrupted = false;
    currentSpinner = null;
    constructor() {
        this.setupInterruptHandler();
    }
    async getInput(prompt = '💬 You: ') {
        this.isInterrupted = false;
        const answer = await inquirer_1.default.prompt([
            {
                type: 'input',
                name: 'message',
                message: prompt,
                validate: (input) => {
                    if (input.trim().length === 0) {
                        return 'Please enter a message';
                    }
                    return true;
                }
            }
        ]);
        return answer.message.trim();
    }
    displayMessage(message, config) {
        const { role, content } = message;
        switch (role) {
            case 'user':
                console.log(chalk_1.default.blue(`💬 You: ${content}`));
                break;
            case 'assistant':
                if (config?.streaming) {
                    // For streaming, content is displayed as it comes
                    process.stdout.write(chalk_1.default.green(content));
                }
                else {
                    console.log(chalk_1.default.green(`🤖 Arien: ${content}`));
                }
                break;
            case 'system':
                console.log(chalk_1.default.gray(`⚙️  System: ${content}`));
                break;
            case 'tool':
                console.log(chalk_1.default.yellow(`🔧 Tool: ${content}`));
                break;
            default:
                console.log(chalk_1.default.white(content));
        }
    }
    displayToolExecution(toolName, status, details) {
        switch (status) {
            case 'start':
                console.log(chalk_1.default.cyan(`🔧 Executing ${toolName}...`));
                if (details) {
                    console.log(chalk_1.default.gray(`   ${details}`));
                }
                break;
            case 'success':
                console.log(chalk_1.default.green(`✅ ${toolName} completed successfully`));
                if (details) {
                    console.log(chalk_1.default.gray(`   ${details}`));
                }
                break;
            case 'error':
                console.log(chalk_1.default.red(`❌ ${toolName} failed`));
                if (details) {
                    console.log(chalk_1.default.red(`   ${details}`));
                }
                break;
        }
    }
    startStreaming(initialMessage) {
        if (initialMessage) {
            process.stdout.write(chalk_1.default.green(`🤖 Arien: ${initialMessage}`));
        }
        else {
            process.stdout.write(chalk_1.default.green('🤖 Arien: '));
        }
    }
    streamChunk(chunk) {
        process.stdout.write(chalk_1.default.green(chunk));
    }
    endStreaming() {
        console.log(); // New line after streaming
    }
    showThinking(message) {
        this.currentSpinner = new spinner_1.SpinnerComponent();
        this.currentSpinner.start(message);
        return this.currentSpinner;
    }
    hideThinking() {
        if (this.currentSpinner) {
            this.currentSpinner.stop();
            this.currentSpinner = null;
        }
    }
    displayError(error, context) {
        console.log(chalk_1.default.red(`❌ Error: ${error}`));
        if (context) {
            console.log(chalk_1.default.gray(`   Context: ${context}`));
        }
    }
    displayWarning(warning) {
        console.log(chalk_1.default.yellow(`⚠️  Warning: ${warning}`));
    }
    displayInfo(info) {
        console.log(chalk_1.default.blue(`ℹ️  ${info}`));
    }
    displaySuccess(message) {
        console.log(chalk_1.default.green(`✅ ${message}`));
    }
    displaySeparator() {
        console.log(chalk_1.default.gray('─'.repeat(60)));
    }
    displaySessionInfo(context) {
        const { config, session } = context;
        console.log(chalk_1.default.gray(`📊 Session: ${session.id.slice(0, 8)} | Provider: ${config.provider} | Model: ${config.model}`));
    }
    displayRetryAttempt(attempt, maxRetries, reason) {
        console.log(chalk_1.default.yellow(`🔄 Retry ${attempt}/${maxRetries}: ${reason}`));
    }
    displayProgress(current, total, operation) {
        const percentage = Math.round((current / total) * 100);
        const progressBar = '█'.repeat(Math.floor(percentage / 5)) + '░'.repeat(20 - Math.floor(percentage / 5));
        process.stdout.write(`\r🔄 ${operation}: [${progressBar}] ${percentage}%`);
        if (current === total) {
            console.log(); // New line when complete
        }
    }
    clearLine() {
        process.stdout.write('\r\x1b[K');
    }
    setupInterruptHandler() {
        let escapeCount = 0;
        let lastEscapeTime = 0;
        // Only set up raw mode if stdin is a TTY
        if (process.stdin.isTTY) {
            process.stdin.setRawMode(true);
            process.stdin.resume();
            process.stdin.setEncoding('utf8');
            process.stdin.on('data', (key) => {
                // Check for double ESC press
                if (key === '\u001b') { // ESC key
                    const now = Date.now();
                    if (now - lastEscapeTime < 500) { // Within 500ms
                        escapeCount++;
                        if (escapeCount >= 2) {
                            this.handleInterrupt();
                            escapeCount = 0;
                        }
                    }
                    else {
                        escapeCount = 1;
                    }
                    lastEscapeTime = now;
                }
                else {
                    escapeCount = 0;
                }
                // Handle Ctrl+C
                if (key === '\u0003') {
                    this.handleInterrupt();
                }
            });
        }
        // Also handle SIGINT for non-TTY environments
        process.on('SIGINT', () => {
            this.handleInterrupt();
        });
    }
    handleInterrupt() {
        this.isInterrupted = true;
        if (this.currentSpinner) {
            this.currentSpinner.stop();
        }
        console.log(chalk_1.default.red('\n🛑 Operation interrupted by user'));
        // Reset terminal state
        process.stdout.write('\r\x1b[K');
    }
    isOperationInterrupted() {
        return this.isInterrupted;
    }
    resetInterruptState() {
        this.isInterrupted = false;
    }
    // Utility method for formatted output
    formatOutput(content, options = {}) {
        const { maxWidth = 80, indent = 0, prefix = '' } = options;
        const indentStr = ' '.repeat(indent);
        const effectiveWidth = maxWidth - indent - prefix.length;
        const lines = content.split('\n');
        const wrappedLines = [];
        for (const line of lines) {
            if (line.length <= effectiveWidth) {
                wrappedLines.push(indentStr + prefix + line);
            }
            else {
                // Simple word wrapping
                const words = line.split(' ');
                let currentLine = '';
                for (const word of words) {
                    if ((currentLine + word).length <= effectiveWidth) {
                        currentLine += (currentLine ? ' ' : '') + word;
                    }
                    else {
                        if (currentLine) {
                            wrappedLines.push(indentStr + prefix + currentLine);
                        }
                        currentLine = word;
                    }
                }
                if (currentLine) {
                    wrappedLines.push(indentStr + prefix + currentLine);
                }
            }
        }
        return wrappedLines.join('\n');
    }
    // Method to display help information
    displayHelp() {
        console.log(chalk_1.default.cyan.bold('\n📖 Arien AI CLI Help\n'));
        console.log(chalk_1.default.white('Basic Usage:'));
        console.log(chalk_1.default.gray('  • Type your message and press Enter to chat with AI'));
        console.log(chalk_1.default.gray('  • The AI can execute tools to help accomplish tasks'));
        console.log(chalk_1.default.gray('  • Double-tap ESC to interrupt ongoing operations\n'));
        console.log(chalk_1.default.white('Slash Commands:'));
        console.log(chalk_1.default.gray('  • /help     - Show this help message'));
        console.log(chalk_1.default.gray('  • /provider - Change AI provider settings'));
        console.log(chalk_1.default.gray('  • /model    - Change AI model'));
        console.log(chalk_1.default.gray('  • /clear    - Clear chat history'));
        console.log(chalk_1.default.gray('  • /exit     - Exit the application\n'));
        console.log(chalk_1.default.white('Available Tools:'));
        console.log(chalk_1.default.gray('  • bash      - Execute shell commands'));
        console.log(chalk_1.default.gray('  • grep      - Search file contents'));
        console.log(chalk_1.default.gray('  • glob      - Find files by pattern'));
        console.log(chalk_1.default.gray('  • write     - Create/update files'));
        console.log(chalk_1.default.gray('  • edit      - Edit existing files'));
        console.log(chalk_1.default.gray('  • web       - Search the internet\n'));
    }
}
exports.ChatComponent = ChatComponent;
//# sourceMappingURL=chat.js.map