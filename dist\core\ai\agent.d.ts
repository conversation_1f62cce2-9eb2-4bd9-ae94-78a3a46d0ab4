import { CommandContext } from '../../types';
export declare class AIAgent {
    private provider;
    private toolManager;
    private chatComponent;
    private retryManager;
    constructor();
    initialize(context: CommandContext): Promise<void>;
    processMessage(userMessage: string, context: CommandContext): Promise<void>;
    private processWithAI;
    private executeTools;
    private shouldRetry;
    private shouldRetryTool;
    private formatToolArgs;
    private formatToolResult;
    private formatFileSize;
    shutdown(): Promise<void>;
}
//# sourceMappingURL=agent.d.ts.map