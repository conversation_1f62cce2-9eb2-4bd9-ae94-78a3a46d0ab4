"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeepseekProvider = void 0;
const axios_1 = __importDefault(require("axios"));
class DeepseekProvider {
    apiKey;
    name = 'deepseek';
    baseUrl = 'https://api.deepseek.com/v1';
    models = ['deepseek-chat', 'deepseek-reasoner'];
    defaultModel = 'deepseek-chat';
    constructor(apiKey) {
        this.apiKey = apiKey;
        if (!apiKey) {
            throw new Error('Deepseek API key is required');
        }
    }
    async sendMessage(messages, tools, model = this.defaultModel, options = {}) {
        try {
            const requestBody = {
                model,
                messages: messages.map(msg => ({
                    role: msg.role,
                    content: msg.content,
                    tool_calls: msg.toolCalls,
                    tool_call_id: msg.toolCallId
                })),
                tools: tools?.map(tool => ({
                    type: tool.type,
                    function: tool.function
                })),
                temperature: options.temperature ?? 0.7,
                max_tokens: options.maxTokens ?? 4000,
                stream: options.stream ?? false
            };
            const response = await axios_1.default.post(`${this.baseUrl}/chat/completions`, requestBody, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                timeout: 30000
            });
            const choice = response.data.choices[0];
            if (!choice) {
                throw new Error('No response choice received from Deepseek');
            }
            const message = {
                role: 'assistant',
                content: choice.message.content || '',
                toolCalls: choice.message.tool_calls?.map(tc => ({
                    id: tc.id,
                    type: tc.type,
                    function: {
                        name: tc.function.name,
                        arguments: tc.function.arguments
                    }
                }))
            };
            return message;
        }
        catch (error) {
            if (axios_1.default.isAxiosError(error)) {
                const status = error.response?.status;
                const message = error.response?.data?.error?.message || error.message;
                if (status === 401) {
                    throw new Error('Invalid Deepseek API key');
                }
                else if (status === 429) {
                    throw new Error('Rate limit exceeded. Please try again later.');
                }
                else if (status === 500) {
                    throw new Error('Deepseek server error. Please try again.');
                }
                else {
                    throw new Error(`Deepseek API error: ${message}`);
                }
            }
            throw error;
        }
    }
    async streamMessage(messages, tools, model = this.defaultModel, onChunk) {
        try {
            const requestBody = {
                model,
                messages: messages.map(msg => ({
                    role: msg.role,
                    content: msg.content,
                    tool_calls: msg.toolCalls,
                    tool_call_id: msg.toolCallId
                })),
                tools: tools?.map(tool => ({
                    type: tool.type,
                    function: tool.function
                })),
                temperature: 0.7,
                max_tokens: 4000,
                stream: true
            };
            const response = await axios_1.default.post(`${this.baseUrl}/chat/completions`, requestBody, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json'
                },
                responseType: 'stream',
                timeout: 60000
            });
            let fullContent = '';
            let toolCalls = [];
            return new Promise((resolve, reject) => {
                response.data.on('data', (chunk) => {
                    const lines = chunk.toString().split('\n');
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data === '[DONE]') {
                                resolve({
                                    role: 'assistant',
                                    content: fullContent,
                                    toolCalls: toolCalls.length > 0 ? toolCalls : undefined
                                });
                                return;
                            }
                            try {
                                const parsed = JSON.parse(data);
                                const delta = parsed.choices?.[0]?.delta;
                                if (delta?.content) {
                                    fullContent += delta.content;
                                    onChunk?.(delta.content);
                                }
                                if (delta?.tool_calls) {
                                    // Handle tool calls in streaming
                                    for (const tc of delta.tool_calls) {
                                        if (tc.function) {
                                            toolCalls.push({
                                                id: tc.id,
                                                type: 'function',
                                                function: {
                                                    name: tc.function.name,
                                                    arguments: tc.function.arguments
                                                }
                                            });
                                        }
                                    }
                                }
                            }
                            catch (parseError) {
                                // Ignore parsing errors for incomplete chunks
                            }
                        }
                    }
                });
                response.data.on('error', (error) => {
                    reject(error);
                });
                response.data.on('end', () => {
                    resolve({
                        role: 'assistant',
                        content: fullContent,
                        toolCalls: toolCalls.length > 0 ? toolCalls : undefined
                    });
                });
            });
        }
        catch (error) {
            if (axios_1.default.isAxiosError(error)) {
                const message = error.response?.data?.error?.message || error.message;
                throw new Error(`Deepseek streaming error: ${message}`);
            }
            throw error;
        }
    }
    validateModel(model) {
        return this.models.includes(model);
    }
    getAvailableModels() {
        return [...this.models];
    }
}
exports.DeepseekProvider = DeepseekProvider;
//# sourceMappingURL=deepseek.js.map