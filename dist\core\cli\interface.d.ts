export declare class CLIInterface {
    private loginComponent;
    private chatComponent;
    private slashCommands;
    private aiAgent;
    private context;
    constructor();
    start(): Promise<void>;
    private displayBanner;
    private initializeConfiguration;
    private createNewSession;
    private runInteractiveSession;
    private isExitCommand;
    private shutdown;
    setupGracefulShutdown(): void;
    static run(): Promise<void>;
}
//# sourceMappingURL=interface.d.ts.map