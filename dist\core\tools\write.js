"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WriteTool = exports.writeToolDefinition = void 0;
const fs_1 = require("fs");
const path_1 = __importDefault(require("path"));
exports.writeToolDefinition = {
    type: 'function',
    function: {
        name: 'write',
        description: `File writing tool that creates or updates files in the filesystem, allowing you to save or modify text content.

WHEN TO USE:
- Creating new files from scratch
- Saving generated content (code, configs, documentation)
- Completely replacing file contents
- Writing data exports or reports
- Creating project templates and boilerplates
- Saving user input or processed data

WHEN NOT TO USE:
- Small edits to existing files (use edit tool instead)
- Appending content to files (use edit tool instead)
- Binary file operations
- Moving or renaming files (use bash tool with 'mv' command)

USAGE NOTES:
- Creates parent directories automatically if they don't exist
- Overwrites existing files completely
- Supports various text encodings (defaults to UTF-8)
- Handles cross-platform path separators
- Preserves file permissions when overwriting
- Atomic write operations to prevent corruption

EXAMPLES:
- Create package.json with project configuration
- Generate TypeScript configuration files
- Save processed data as JSON or CSV
- Create HTML templates or documentation
- Write shell scripts or batch files
- Generate code files from templates`,
        parameters: {
            type: 'object',
            properties: {
                path: {
                    type: 'string',
                    description: 'File path where content should be written'
                },
                content: {
                    type: 'string',
                    description: 'Text content to write to the file'
                },
                encoding: {
                    type: 'string',
                    description: 'Text encoding for the file (optional, defaults to utf8)',
                    enum: ['utf8', 'ascii', 'base64', 'hex', 'latin1', 'utf16le']
                },
                createDirectories: {
                    type: 'boolean',
                    description: 'Create parent directories if they don\'t exist (optional, defaults to true)'
                }
            },
            required: ['path', 'content']
        }
    }
};
class WriteTool {
    async execute(options) {
        const startTime = Date.now();
        try {
            const { path: filePath, content, encoding = 'utf8', createDirectories = true } = options;
            // Validate inputs
            if (!filePath || filePath.trim().length === 0) {
                return {
                    success: false,
                    output: '',
                    error: 'File path cannot be empty'
                };
            }
            if (content === undefined || content === null) {
                return {
                    success: false,
                    output: '',
                    error: 'Content cannot be null or undefined'
                };
            }
            // Resolve and normalize the file path
            const resolvedPath = path_1.default.resolve(filePath);
            const directory = path_1.default.dirname(resolvedPath);
            // Check if we're trying to write to a dangerous location
            if (this.isDangerousPath(resolvedPath)) {
                return {
                    success: false,
                    output: '',
                    error: 'Cannot write to system or protected directories'
                };
            }
            // Create parent directories if needed
            if (createDirectories) {
                try {
                    await fs_1.promises.mkdir(directory, { recursive: true });
                }
                catch (error) {
                    return {
                        success: false,
                        output: '',
                        error: `Failed to create directory ${directory}: ${error instanceof Error ? error.message : String(error)}`
                    };
                }
            }
            // Check if file already exists to provide appropriate feedback
            let fileExisted = false;
            let originalSize = 0;
            try {
                const stat = await fs_1.promises.stat(resolvedPath);
                fileExisted = true;
                originalSize = stat.size;
            }
            catch {
                // File doesn't exist, which is fine
            }
            // Write the file
            await fs_1.promises.writeFile(resolvedPath, content, { encoding: encoding });
            // Get file stats after writing
            const stat = await fs_1.promises.stat(resolvedPath);
            const executionTime = Date.now() - startTime;
            // Format success message
            const action = fileExisted ? 'Updated' : 'Created';
            const sizeInfo = `${this.formatFileSize(stat.size)}`;
            const relativePath = path_1.default.relative(process.cwd(), resolvedPath);
            let output = `${action} file: ${relativePath} (${sizeInfo})`;
            if (fileExisted) {
                const sizeDiff = stat.size - originalSize;
                const diffStr = sizeDiff > 0 ? `+${this.formatFileSize(sizeDiff)}` :
                    sizeDiff < 0 ? `-${this.formatFileSize(Math.abs(sizeDiff))}` : 'no change';
                output += ` [${diffStr}]`;
            }
            return {
                success: true,
                output,
                metadata: {
                    filePath: resolvedPath,
                    relativePath,
                    fileSize: stat.size,
                    encoding,
                    fileExisted,
                    originalSize,
                    linesWritten: content.split('\n').length,
                    executionTime,
                    directory
                }
            };
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            if (error instanceof Error) {
                // Handle specific error types
                if (error.code === 'EACCES') {
                    return {
                        success: false,
                        output: '',
                        error: `Permission denied: Cannot write to ${options.path}`,
                        metadata: { executionTime }
                    };
                }
                if (error.code === 'ENOENT') {
                    return {
                        success: false,
                        output: '',
                        error: `Directory does not exist: ${path_1.default.dirname(options.path)}`,
                        metadata: { executionTime }
                    };
                }
                if (error.code === 'ENOSPC') {
                    return {
                        success: false,
                        output: '',
                        error: 'No space left on device',
                        metadata: { executionTime }
                    };
                }
                if (error.code === 'EISDIR') {
                    return {
                        success: false,
                        output: '',
                        error: `Path is a directory, not a file: ${options.path}`,
                        metadata: { executionTime }
                    };
                }
            }
            return {
                success: false,
                output: '',
                error: `Failed to write file: ${error instanceof Error ? error.message : String(error)}`,
                metadata: {
                    filePath: options.path,
                    executionTime
                }
            };
        }
    }
    isDangerousPath(filePath) {
        const dangerous = [
            '/etc',
            '/bin',
            '/sbin',
            '/usr/bin',
            '/usr/sbin',
            '/boot',
            '/sys',
            '/proc',
            'C:\\Windows',
            'C:\\Program Files',
            'C:\\Program Files (x86)'
        ];
        const normalizedPath = path_1.default.normalize(filePath);
        return dangerous.some(dangerousPath => {
            const normalizedDangerous = path_1.default.normalize(dangerousPath);
            return normalizedPath.startsWith(normalizedDangerous);
        });
    }
    formatFileSize(bytes) {
        if (bytes === 0)
            return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
    // Utility method to backup existing file before overwriting
    async backupFile(filePath) {
        try {
            const resolvedPath = path_1.default.resolve(filePath);
            const stat = await fs_1.promises.stat(resolvedPath);
            if (stat.isFile()) {
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
                const backupPath = `${resolvedPath}.backup.${timestamp}`;
                await fs_1.promises.copyFile(resolvedPath, backupPath);
                return backupPath;
            }
        }
        catch {
            // File doesn't exist or can't be backed up
        }
        return null;
    }
    // Utility method to validate file content
    validateContent(content, options = {}) {
        const { maxSize = 10 * 1024 * 1024, allowEmpty = true } = options; // 10MB default
        if (!allowEmpty && content.trim().length === 0) {
            return { valid: false, error: 'Content cannot be empty' };
        }
        if (Buffer.byteLength(content, 'utf8') > maxSize) {
            return { valid: false, error: `Content too large (max ${this.formatFileSize(maxSize)})` };
        }
        return { valid: true };
    }
    // Utility method to suggest file extension based on content
    suggestExtension(content) {
        const trimmed = content.trim();
        if (trimmed.startsWith('{') || trimmed.startsWith('[')) {
            return '.json';
        }
        if (trimmed.includes('<!DOCTYPE html') || trimmed.includes('<html')) {
            return '.html';
        }
        if (trimmed.includes('function ') || trimmed.includes('const ') || trimmed.includes('import ')) {
            return '.js';
        }
        if (trimmed.includes('interface ') || trimmed.includes('type ') || trimmed.includes(': string')) {
            return '.ts';
        }
        if (trimmed.startsWith('#') || trimmed.includes('## ')) {
            return '.md';
        }
        return '.txt';
    }
}
exports.WriteTool = WriteTool;
//# sourceMappingURL=write.js.map