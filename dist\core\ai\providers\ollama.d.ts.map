{"version": 3, "file": "ollama.d.ts", "sourceRoot": "", "sources": ["../../../../src/core/ai/providers/ollama.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,SAAS,EAAY,cAAc,EAAE,MAAM,SAAS,CAAC;AAE1E,MAAM,WAAW,cAAc;IAC7B,KAAK,EAAE,MAAM,CAAC;IACd,UAAU,EAAE,MAAM,CAAC;IACnB,OAAO,EAAE;QACP,IAAI,EAAE,MAAM,CAAC;QACb,OAAO,EAAE,MAAM,CAAC;QAChB,UAAU,CAAC,EAAE,KAAK,CAAC;YACjB,QAAQ,EAAE;gBACR,IAAI,EAAE,MAAM,CAAC;gBACb,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;aAChC,CAAC;SACH,CAAC,CAAC;KACJ,CAAC;IACF,IAAI,EAAE,OAAO,CAAC;IACd,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,iBAAiB,CAAC,EAAE,MAAM,CAAC;IAC3B,oBAAoB,CAAC,EAAE,MAAM,CAAC;IAC9B,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB;AAED,MAAM,WAAW,WAAW;IAC1B,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,WAAW,EAAE,MAAM,CAAC;IACpB,IAAI,EAAE,MAAM,CAAC;IACb,MAAM,EAAE,MAAM,CAAC;IACf,OAAO,EAAE;QACP,YAAY,EAAE,MAAM,CAAC;QACrB,MAAM,EAAE,MAAM,CAAC;QACf,MAAM,EAAE,MAAM,CAAC;QACf,QAAQ,EAAE,MAAM,EAAE,CAAC;QACnB,cAAc,EAAE,MAAM,CAAC;QACvB,kBAAkB,EAAE,MAAM,CAAC;KAC5B,CAAC;CACH;AAED,qBAAa,cAAe,YAAW,UAAU;aAM7B,OAAO,EAAE,MAAM;aACf,MAAM,EAAE,MAAM;IANhC,SAAgB,IAAI,YAAY;IAChC,SAAgB,MAAM,EAAE,MAAM,EAAE,CAAM;IAC/B,YAAY,SAAc;gBAGf,OAAO,GAAE,MAAiC,EAC1C,MAAM,GAAE,MAAW;IAG/B,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAQ3B,mBAAmB,IAAI,OAAO,CAAC,IAAI,CAAC;IAwBpC,WAAW,CACf,QAAQ,EAAE,SAAS,EAAE,EACrB,KAAK,CAAC,EAAE,cAAc,EAAE,EACxB,KAAK,GAAE,MAA0B,EACjC,OAAO,GAAE;QACP,WAAW,CAAC,EAAE,MAAM,CAAC;QACrB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,MAAM,CAAC,EAAE,OAAO,CAAC;KACb,GACL,OAAO,CAAC,SAAS,CAAC;IA2Df,aAAa,CACjB,QAAQ,EAAE,SAAS,EAAE,EACrB,KAAK,CAAC,EAAE,cAAc,EAAE,EACxB,KAAK,GAAE,MAA0B,EACjC,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,IAAI,GAChC,OAAO,CAAC,SAAS,CAAC;IA6Ff,SAAS,CAAC,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAqBjD,aAAa,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO;IAIrC,kBAAkB,IAAI,MAAM,EAAE;IAIxB,eAAe,IAAI,OAAO,CAAC,OAAO,CAAC;CAQ1C"}