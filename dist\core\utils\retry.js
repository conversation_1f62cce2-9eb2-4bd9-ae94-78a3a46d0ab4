"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ErrorClassifier = exports.RetryManager = void 0;
exports.createNetworkRetryOptions = createNetworkRetryOptions;
exports.createRateLimitRetryOptions = createRateLimitRetryOptions;
exports.createFileOperationRetryOptions = createFileOperationRetryOptions;
class RetryManager {
    activeOperations = new Set();
    async executeWithRetry(operation, options, onRetry) {
        const { maxRetries, baseDelay, maxDelay, exponentialBackoff, retryCondition } = options;
        let lastError;
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                const promise = operation();
                this.activeOperations.add(promise);
                const result = await promise;
                this.activeOperations.delete(promise);
                return result;
            }
            catch (error) {
                const err = error instanceof Error ? error : new Error(String(error));
                lastError = err;
                // Don't retry on the last attempt
                if (attempt === maxRetries) {
                    break;
                }
                // Check if we should retry this error
                if (retryCondition && !retryCondition(err)) {
                    break;
                }
                // Call retry callback
                if (onRetry) {
                    onRetry(attempt + 1, err);
                }
                // Calculate delay
                const delay = this.calculateDelay(attempt, baseDelay, maxDelay, exponentialBackoff);
                // Wait before retrying
                await this.sleep(delay);
            }
        }
        throw lastError;
    }
    calculateDelay(attempt, baseDelay, maxDelay, exponentialBackoff) {
        if (!exponentialBackoff) {
            return Math.min(baseDelay, maxDelay);
        }
        // Exponential backoff with jitter
        const exponentialDelay = baseDelay * Math.pow(2, attempt);
        const jitter = Math.random() * 0.1 * exponentialDelay; // 10% jitter
        return Math.min(exponentialDelay + jitter, maxDelay);
    }
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    cancelAll() {
        // Note: This is a simplified cancellation mechanism
        // In a real implementation, you might want to use AbortController
        this.activeOperations.clear();
    }
    getActiveOperationCount() {
        return this.activeOperations.size;
    }
}
exports.RetryManager = RetryManager;
// Utility functions for common retry scenarios
function createNetworkRetryOptions() {
    return {
        maxRetries: 3,
        baseDelay: 1000,
        maxDelay: 10000,
        exponentialBackoff: true,
        retryCondition: (error) => {
            const networkErrors = [
                'ECONNRESET',
                'ENOTFOUND',
                'ECONNREFUSED',
                'ETIMEDOUT',
                'timeout',
                'network'
            ];
            const errorMessage = error.message.toLowerCase();
            return networkErrors.some(netError => errorMessage.includes(netError));
        }
    };
}
function createRateLimitRetryOptions() {
    return {
        maxRetries: 5,
        baseDelay: 2000,
        maxDelay: 30000,
        exponentialBackoff: true,
        retryCondition: (error) => {
            const rateLimitErrors = [
                'rate limit',
                'too many requests',
                '429',
                'quota exceeded'
            ];
            const errorMessage = error.message.toLowerCase();
            return rateLimitErrors.some(rlError => errorMessage.includes(rlError));
        }
    };
}
function createFileOperationRetryOptions() {
    return {
        maxRetries: 2,
        baseDelay: 500,
        maxDelay: 2000,
        exponentialBackoff: false,
        retryCondition: (error) => {
            const fileErrors = [
                'EBUSY',
                'EMFILE',
                'ENFILE',
                'temporarily unavailable'
            ];
            const errorMessage = error.message.toLowerCase();
            return fileErrors.some(fileError => errorMessage.includes(fileError));
        }
    };
}
// Error classification utility
class ErrorClassifier {
    static isNetworkError(error) {
        const networkIndicators = [
            'ECONNRESET',
            'ENOTFOUND',
            'ECONNREFUSED',
            'ETIMEDOUT',
            'network',
            'timeout',
            'dns'
        ];
        const message = error.message.toLowerCase();
        return networkIndicators.some(indicator => message.includes(indicator));
    }
    static isRateLimitError(error) {
        const rateLimitIndicators = [
            'rate limit',
            'too many requests',
            '429',
            'quota exceeded',
            'throttled'
        ];
        const message = error.message.toLowerCase();
        return rateLimitIndicators.some(indicator => message.includes(indicator));
    }
    static isTemporaryError(error) {
        const temporaryIndicators = [
            'temporary',
            'temporarily',
            'try again',
            'retry',
            'busy',
            'unavailable',
            '503',
            '502',
            '504'
        ];
        const message = error.message.toLowerCase();
        return temporaryIndicators.some(indicator => message.includes(indicator));
    }
    static isPermanentError(error) {
        const permanentIndicators = [
            'unauthorized',
            'forbidden',
            'not found',
            'invalid',
            'malformed',
            'syntax error',
            '401',
            '403',
            '404',
            '400'
        ];
        const message = error.message.toLowerCase();
        return permanentIndicators.some(indicator => message.includes(indicator));
    }
    static shouldRetry(error) {
        // Don't retry permanent errors
        if (this.isPermanentError(error)) {
            return false;
        }
        // Retry network, rate limit, and temporary errors
        return this.isNetworkError(error) ||
            this.isRateLimitError(error) ||
            this.isTemporaryError(error);
    }
    static getRetryDelay(error, attempt) {
        if (this.isRateLimitError(error)) {
            // Longer delays for rate limits
            return Math.min(2000 * Math.pow(2, attempt), 30000);
        }
        if (this.isNetworkError(error)) {
            // Medium delays for network errors
            return Math.min(1000 * Math.pow(1.5, attempt), 10000);
        }
        // Short delays for other temporary errors
        return Math.min(500 * Math.pow(1.2, attempt), 5000);
    }
}
exports.ErrorClassifier = ErrorClassifier;
//# sourceMappingURL=retry.js.map