"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoginComponent = void 0;
const inquirer_1 = __importDefault(require("inquirer"));
const deepseek_1 = require("../../../core/ai/providers/deepseek");
const ollama_1 = require("../../../core/ai/providers/ollama");
const chalk_1 = __importDefault(require("chalk"));
const boxen_1 = __importDefault(require("boxen"));
class LoginComponent {
    async configure() {
        console.log((0, boxen_1.default)(chalk_1.default.cyan.bold('🤖 Arien AI CLI Configuration\n') +
            chalk_1.default.white('Configure your AI provider to get started'), {
            padding: 1,
            margin: 1,
            borderStyle: 'round',
            borderColor: 'cyan'
        }));
        const config = await this.promptForConfiguration();
        await this.validateConfiguration(config);
        console.log(chalk_1.default.green('✅ Configuration completed successfully!\n'));
        return config;
    }
    async promptForConfiguration() {
        const answers = await inquirer_1.default.prompt([
            {
                type: 'list',
                name: 'provider',
                message: 'Select your AI provider:',
                choices: [
                    {
                        name: '🧠 Deepseek (Cloud-based, requires API key)',
                        value: 'deepseek'
                    },
                    {
                        name: '🏠 Ollama (Local, requires Ollama installation)',
                        value: 'ollama'
                    }
                ]
            }
        ]);
        if (answers.provider === 'deepseek') {
            return await this.configureDeepseek();
        }
        else {
            return await this.configureOllama();
        }
    }
    async configureDeepseek() {
        console.log(chalk_1.default.yellow('\n📋 Deepseek Configuration'));
        console.log(chalk_1.default.gray('Get your API key from: https://platform.deepseek.com/api_keys\n'));
        const answers = await inquirer_1.default.prompt([
            {
                type: 'password',
                name: 'apiKey',
                message: 'Enter your Deepseek API key:',
                mask: '*',
                validate: (input) => {
                    if (!input || input.trim().length === 0) {
                        return 'API key is required';
                    }
                    if (input.length < 10) {
                        return 'API key seems too short';
                    }
                    return true;
                }
            },
            {
                type: 'list',
                name: 'model',
                message: 'Select the model to use:',
                choices: [
                    {
                        name: 'deepseek-chat (Recommended for general tasks)',
                        value: 'deepseek-chat'
                    },
                    {
                        name: 'deepseek-reasoner (Better for complex reasoning)',
                        value: 'deepseek-reasoner'
                    }
                ],
                default: 'deepseek-chat'
            }
        ]);
        return {
            provider: 'deepseek',
            apiKey: answers.apiKey.trim(),
            model: answers.model,
            maxRetries: 3,
            retryDelay: 1000,
            timeout: 30000
        };
    }
    async configureOllama() {
        console.log(chalk_1.default.yellow('\n🏠 Ollama Configuration'));
        console.log(chalk_1.default.gray('Make sure Ollama is installed and running locally\n'));
        const answers = await inquirer_1.default.prompt([
            {
                type: 'input',
                name: 'baseUrl',
                message: 'Ollama server URL:',
                default: 'http://localhost:11434',
                validate: (input) => {
                    try {
                        new URL(input);
                        return true;
                    }
                    catch {
                        return 'Please enter a valid URL';
                    }
                }
            }
        ]);
        // Test connection to Ollama
        console.log(chalk_1.default.blue('🔍 Checking Ollama connection...'));
        try {
            const ollama = new ollama_1.OllamaProvider(answers.baseUrl);
            const isConnected = await ollama.checkConnection();
            if (!isConnected) {
                throw new Error('Cannot connect to Ollama');
            }
            await ollama.initialize();
            const models = ollama.getAvailableModels();
            if (models.length === 0) {
                console.log(chalk_1.default.yellow('⚠️  No models found. You may need to pull a model first.'));
                console.log(chalk_1.default.gray('Example: ollama pull llama3.2\n'));
                const { shouldContinue } = await inquirer_1.default.prompt([
                    {
                        type: 'confirm',
                        name: 'shouldContinue',
                        message: 'Continue without selecting a model? (You can configure it later)',
                        default: true
                    }
                ]);
                if (!shouldContinue) {
                    throw new Error('Configuration cancelled');
                }
                return {
                    provider: 'ollama',
                    baseUrl: answers.baseUrl,
                    model: 'llama3.2', // Default fallback
                    maxRetries: 3,
                    retryDelay: 1000,
                    timeout: 60000
                };
            }
            const { model } = await inquirer_1.default.prompt([
                {
                    type: 'list',
                    name: 'model',
                    message: 'Select a model:',
                    choices: models.map(model => ({
                        name: model,
                        value: model
                    }))
                }
            ]);
            return {
                provider: 'ollama',
                baseUrl: answers.baseUrl,
                model,
                maxRetries: 3,
                retryDelay: 1000,
                timeout: 60000
            };
        }
        catch (error) {
            console.log(chalk_1.default.red(`❌ Failed to connect to Ollama: ${error instanceof Error ? error.message : String(error)}`));
            console.log(chalk_1.default.gray('Make sure Ollama is installed and running:'));
            console.log(chalk_1.default.gray('1. Install: https://ollama.ai/download'));
            console.log(chalk_1.default.gray('2. Start: ollama serve'));
            console.log(chalk_1.default.gray('3. Pull a model: ollama pull llama3.2\n'));
            throw new Error('Ollama configuration failed');
        }
    }
    async validateConfiguration(config) {
        console.log(chalk_1.default.blue('🔍 Validating configuration...'));
        try {
            if (config.provider === 'deepseek') {
                const provider = new deepseek_1.DeepseekProvider(config.apiKey);
                // Test with a simple message
                await provider.sendMessage([
                    { role: 'user', content: 'Hello, this is a test message.' }
                ], undefined, config.model);
                console.log(chalk_1.default.green('✅ Deepseek connection successful'));
            }
            else if (config.provider === 'ollama') {
                const provider = new ollama_1.OllamaProvider(config.baseUrl);
                await provider.initialize();
                if (!provider.validateModel(config.model)) {
                    console.log(chalk_1.default.yellow(`⚠️  Model '${config.model}' not found, but configuration saved`));
                }
                else {
                    // Test with a simple message
                    await provider.sendMessage([
                        { role: 'user', content: 'Hello, this is a test message.' }
                    ], undefined, config.model);
                    console.log(chalk_1.default.green('✅ Ollama connection successful'));
                }
            }
        }
        catch (error) {
            console.log(chalk_1.default.yellow(`⚠️  Configuration saved but validation failed: ${error instanceof Error ? error.message : String(error)}`));
            console.log(chalk_1.default.gray('You can reconfigure later using the /provider command\n'));
        }
    }
    displayWelcome() {
        console.log((0, boxen_1.default)(chalk_1.default.green.bold('🎉 Welcome to Arien AI CLI!\n') +
            chalk_1.default.white('Your local agentic terminal system is ready.\n\n') +
            chalk_1.default.cyan('Available commands:\n') +
            chalk_1.default.gray('• Type your message to chat with AI\n') +
            chalk_1.default.gray('• Use "/" for slash commands\n') +
            chalk_1.default.gray('• Double-tap ESC to interrupt operations\n') +
            chalk_1.default.gray('• Type "exit" or "quit" to leave'), {
            padding: 1,
            margin: 1,
            borderStyle: 'double',
            borderColor: 'green'
        }));
    }
}
exports.LoginComponent = LoginComponent;
//# sourceMappingURL=login.js.map