{"name": "arien-ai-cli", "version": "1.0.0", "description": "Powerful Local Agentic CLI Terminal System powered by AI with LLMs Function Tools", "main": "dist/index.js", "bin": {"arien": "dist/index.js"}, "scripts": {"build": "tsc && tsc-alias", "dev": "tsx src/index.ts", "start": "node dist/index.js", "watch": "tsx watch src/index.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "postbuild": "node -e \"const fs = require('fs'); const path = require('path'); const file = path.join('dist', 'index.js'); if (fs.existsSync(file) && process.platform !== 'win32') { fs.chmodSync(file, '755'); }\"", "install-global": "npm run build && npm install -g .", "uninstall-global": "npm uninstall -g arien-ai-cli", "install-script": "tsx scripts/install.ts", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["ai", "cli", "terminal", "agent", "llm", "tools", "automation"], "author": "Arien AI", "license": "MIT", "engines": {"node": ">=22.0.0"}, "dependencies": {"@types/node": "^22.10.2", "axios": "^1.7.9", "blessed": "^0.1.81", "boxen": "^8.0.1", "chalk": "^5.3.0", "cli-cursor": "^5.0.0", "cli-progress": "^3.12.0", "cli-spinners": "^3.2.0", "commander": "^12.1.0", "conf": "^13.0.1", "execa": "^9.5.1", "fast-glob": "^3.3.2", "figures": "^6.1.0", "inquirer": "^12.1.0", "node-fetch": "^3.3.2", "ora": "^8.1.1", "strip-ansi": "^7.1.0", "terminal-kit": "^3.1.1", "uuid": "^11.0.3", "wrap-ansi": "^9.0.0"}, "devDependencies": {"@types/blessed": "^0.1.25", "@types/cli-progress": "^3.11.6", "@types/inquirer": "^9.0.7", "@types/jest": "^29.5.14", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "eslint": "^9.17.0", "jest": "^29.7.0", "nodemon": "^3.1.9", "prettier": "^3.4.2", "rimraf": "^6.0.1", "ts-jest": "^29.2.5", "tsc-alias": "^1.8.16", "tsx": "^4.19.2", "typescript": "^5.8.2"}, "files": ["dist/**/*", "README.md", "LICENSE"], "repository": {"type": "git", "url": "https://github.com/arien-ai/arien-ai-cli.git"}, "bugs": {"url": "https://github.com/arien-ai/arien-ai-cli/issues"}, "homepage": "https://github.com/arien-ai/arien-ai-cli#readme"}