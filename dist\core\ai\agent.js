"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIAgent = void 0;
const deepseek_1 = require("./providers/deepseek");
const ollama_1 = require("./providers/ollama");
const tools_1 = require("../tools");
const system_prompt_1 = require("./system-prompt");
const chat_1 = require("../cli/components/chat");
const retry_1 = require("../utils/retry");
class AIAgent {
    provider = null;
    toolManager;
    chatComponent;
    retryManager;
    constructor() {
        this.toolManager = new tools_1.ToolManager();
        this.chatComponent = new chat_1.ChatComponent();
        this.retryManager = new retry_1.RetryManager();
    }
    async initialize(context) {
        const { config } = context;
        // Initialize AI provider
        if (config.provider === 'deepseek') {
            this.provider = new deepseek_1.DeepseekProvider(config.api<PERSON>ey);
        }
        else if (config.provider === 'ollama') {
            this.provider = new ollama_1.OllamaProvider(config.baseUrl);
            await this.provider.initialize();
        }
        else {
            throw new Error(`Unsupported provider: ${config.provider}`);
        }
    }
    async processMessage(userMessage, context) {
        if (!this.provider) {
            throw new Error('AI provider not initialized');
        }
        // Add user message to session
        const userMsg = { role: 'user', content: userMessage };
        context.session.messages.push(userMsg);
        // Display user message
        this.chatComponent.displayMessage(userMsg);
        // Start processing with retry logic
        await this.retryManager.executeWithRetry(() => this.processWithAI(context), {
            maxRetries: context.config.maxRetries,
            baseDelay: context.config.retryDelay,
            maxDelay: 30000,
            exponentialBackoff: true,
            retryCondition: (error) => this.shouldRetry(error)
        }, (attempt, error) => {
            this.chatComponent.displayRetryAttempt(attempt, context.config.maxRetries, error.message);
        });
    }
    async processWithAI(context) {
        if (!this.provider) {
            throw new Error('AI provider not initialized');
        }
        // Check for interruption
        if (this.chatComponent.isOperationInterrupted()) {
            throw new Error('Operation interrupted by user');
        }
        // Prepare messages with system prompt
        const messages = [
            { role: 'system', content: system_prompt_1.SYSTEM_PROMPT },
            ...context.session.messages
        ];
        // Get tool definitions
        const tools = this.toolManager.getToolDefinitions();
        // Show thinking indicator
        this.chatComponent.showThinking('AI is thinking...');
        try {
            // Get AI response with streaming
            let aiResponse;
            // Try streaming first, fallback to regular if not supported
            if (this.provider?.streamMessage) {
                try {
                    // Hide thinking spinner and start streaming
                    this.chatComponent.hideThinking();
                    this.chatComponent.startStreaming();
                    aiResponse = await this.provider.streamMessage(messages, tools, context.config.model, (chunk) => {
                        if (!this.chatComponent.isOperationInterrupted()) {
                            this.chatComponent.streamChunk(chunk);
                        }
                    });
                    this.chatComponent.endStreaming();
                }
                catch (streamError) {
                    // Fallback to non-streaming if streaming fails
                    this.chatComponent.endStreaming();
                    this.chatComponent.showThinking('AI is thinking...');
                    aiResponse = await this.provider.sendMessage(messages, tools, context.config.model);
                    this.chatComponent.hideThinking();
                    this.chatComponent.displayMessage(aiResponse);
                }
            }
            else {
                // For non-streaming providers, keep spinner running until response is received
                aiResponse = await this.provider.sendMessage(messages, tools, context.config.model);
                this.chatComponent.hideThinking();
                this.chatComponent.displayMessage(aiResponse);
            }
            // Add AI response to session
            context.session.messages.push(aiResponse);
            // Execute tools if requested
            if (aiResponse.toolCalls && aiResponse.toolCalls.length > 0) {
                await this.executeTools(aiResponse.toolCalls, context);
            }
        }
        catch (error) {
            this.chatComponent.hideThinking();
            throw error;
        }
    }
    async executeTools(toolCalls, context) {
        const toolResults = [];
        // Show overall progress
        if (toolCalls.length > 1) {
            this.chatComponent.displayInfo(`Executing ${toolCalls.length} tools...`);
        }
        for (let i = 0; i < toolCalls.length; i++) {
            const toolCall = toolCalls[i];
            if (!toolCall) {
                continue;
            }
            // Check for interruption
            if (this.chatComponent.isOperationInterrupted()) {
                this.chatComponent.displayWarning('Tool execution interrupted by user');
                break;
            }
            // Show progress for multiple tools
            if (toolCalls.length > 1) {
                this.chatComponent.displayProgress(i + 1, toolCalls.length, `Tool ${i + 1}/${toolCalls.length}`);
            }
            try {
                // Parse tool arguments
                let args;
                try {
                    args = JSON.parse(toolCall.function.arguments);
                }
                catch (parseError) {
                    const errorMsg = `Invalid tool arguments for ${toolCall.function.name}: ${parseError}`;
                    this.chatComponent.displayError(errorMsg);
                    toolResults.push({
                        role: 'tool',
                        content: `Error: ${errorMsg}`,
                        toolCallId: toolCall.id
                    });
                    continue;
                }
                // Validate tool call
                const validation = this.toolManager.validateToolCall(toolCall.function.name, args);
                if (!validation.valid) {
                    this.chatComponent.displayError(validation.error, `Tool: ${toolCall.function.name}`);
                    toolResults.push({
                        role: 'tool',
                        content: `Error: ${validation.error}`,
                        toolCallId: toolCall.id
                    });
                    continue;
                }
                // Display tool execution start
                this.chatComponent.displayToolExecution(toolCall.function.name, 'start', this.formatToolArgs(args));
                // Execute tool with retry logic
                const result = await this.retryManager.executeWithRetry(() => this.toolManager.executeTool(toolCall.function.name, args), {
                    maxRetries: 2, // Fewer retries for tools
                    baseDelay: 1000,
                    maxDelay: 5000,
                    exponentialBackoff: true,
                    retryCondition: (error) => this.shouldRetryTool(error)
                }, (attempt, error) => {
                    this.chatComponent.displayRetryAttempt(attempt, 2, `Tool ${toolCall.function.name}: ${error.message}`);
                });
                // Display tool execution result
                if (result.success) {
                    this.chatComponent.displayToolExecution(toolCall.function.name, 'success', this.formatToolResult(result));
                }
                else {
                    this.chatComponent.displayToolExecution(toolCall.function.name, 'error', result.error);
                }
                // Add tool result to messages
                toolResults.push({
                    role: 'tool',
                    content: result.success ? result.output : `Error: ${result.error}`,
                    toolCallId: toolCall.id
                });
            }
            catch (error) {
                const errorMsg = `Tool execution failed: ${error instanceof Error ? error.message : String(error)}`;
                this.chatComponent.displayError(errorMsg, `Tool: ${toolCall.function.name}`);
                toolResults.push({
                    role: 'tool',
                    content: `Error: ${errorMsg}`,
                    toolCallId: toolCall.id
                });
            }
        }
        // Add tool results to session
        context.session.messages.push(...toolResults);
        // If tools were executed, get AI response to tool results
        if (toolResults.length > 0 && !this.chatComponent.isOperationInterrupted()) {
            await this.processWithAI(context);
        }
    }
    shouldRetry(error) {
        const retryableErrors = [
            'ECONNRESET',
            'ENOTFOUND',
            'ECONNREFUSED',
            'ETIMEDOUT',
            'Rate limit',
            'timeout',
            'network',
            'temporary'
        ];
        const errorMessage = error.message.toLowerCase();
        return retryableErrors.some(retryable => errorMessage.includes(retryable));
    }
    shouldRetryTool(error) {
        const retryableToolErrors = [
            'ECONNRESET',
            'ETIMEDOUT',
            'temporary',
            'busy'
        ];
        const errorMessage = error.message.toLowerCase();
        return retryableToolErrors.some(retryable => errorMessage.includes(retryable));
    }
    formatToolArgs(args) {
        try {
            const formatted = Object.entries(args)
                .map(([key, value]) => `${key}: ${typeof value === 'string' ? value.slice(0, 50) : JSON.stringify(value)}`)
                .join(', ');
            return formatted.length > 100 ? formatted.slice(0, 100) + '...' : formatted;
        }
        catch {
            return 'Complex arguments';
        }
    }
    formatToolResult(result) {
        const parts = [];
        if (result.executionTime) {
            parts.push(`${result.executionTime}ms`);
        }
        if (result.metadata) {
            if (result.metadata['fileSize']) {
                parts.push(`${this.formatFileSize(result.metadata['fileSize'])}`);
            }
            if (result.metadata['matchingFiles']) {
                parts.push(`${result.metadata['matchingFiles']} files`);
            }
            if (result.metadata['exitCode'] !== undefined) {
                parts.push(`exit: ${result.metadata['exitCode']}`);
            }
        }
        return parts.join(', ');
    }
    formatFileSize(bytes) {
        if (bytes === 0)
            return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
    // Method to handle graceful shutdown
    async shutdown() {
        this.chatComponent.hideThinking();
        this.retryManager.cancelAll();
    }
}
exports.AIAgent = AIAgent;
//# sourceMappingURL=agent.js.map