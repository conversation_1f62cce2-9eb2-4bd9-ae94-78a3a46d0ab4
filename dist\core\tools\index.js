"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.webToolDefinition = exports.editToolDefinition = exports.writeToolDefinition = exports.globToolDefinition = exports.grepToolDefinition = exports.bashToolDefinition = exports.WebTool = exports.EditTool = exports.WriteTool = exports.GlobTool = exports.GrepTool = exports.BashTool = exports.ToolManager = void 0;
const bash_1 = require("./bash");
Object.defineProperty(exports, "BashTool", { enumerable: true, get: function () { return bash_1.BashTool; } });
Object.defineProperty(exports, "bashToolDefinition", { enumerable: true, get: function () { return bash_1.bashToolDefinition; } });
const grep_1 = require("./grep");
Object.defineProperty(exports, "GrepTool", { enumerable: true, get: function () { return grep_1.GrepTool; } });
Object.defineProperty(exports, "grepToolDefinition", { enumerable: true, get: function () { return grep_1.grepToolDefinition; } });
const glob_1 = require("./glob");
Object.defineProperty(exports, "GlobTool", { enumerable: true, get: function () { return glob_1.GlobTool; } });
Object.defineProperty(exports, "globToolDefinition", { enumerable: true, get: function () { return glob_1.globToolDefinition; } });
const write_1 = require("./write");
Object.defineProperty(exports, "WriteTool", { enumerable: true, get: function () { return write_1.WriteTool; } });
Object.defineProperty(exports, "writeToolDefinition", { enumerable: true, get: function () { return write_1.writeToolDefinition; } });
const edit_1 = require("./edit");
Object.defineProperty(exports, "EditTool", { enumerable: true, get: function () { return edit_1.EditTool; } });
Object.defineProperty(exports, "editToolDefinition", { enumerable: true, get: function () { return edit_1.editToolDefinition; } });
const web_1 = require("./web");
Object.defineProperty(exports, "WebTool", { enumerable: true, get: function () { return web_1.WebTool; } });
Object.defineProperty(exports, "webToolDefinition", { enumerable: true, get: function () { return web_1.webToolDefinition; } });
class ToolManager {
    tools = new Map();
    definitions = new Map();
    constructor() {
        this.initializeTools();
    }
    initializeTools() {
        // Initialize all tools
        this.tools.set('bash', new bash_1.BashTool());
        this.tools.set('grep', new grep_1.GrepTool());
        this.tools.set('glob', new glob_1.GlobTool());
        this.tools.set('write', new write_1.WriteTool());
        this.tools.set('edit', new edit_1.EditTool());
        this.tools.set('web', new web_1.WebTool());
        // Store tool definitions
        this.definitions.set('bash', bash_1.bashToolDefinition);
        this.definitions.set('grep', grep_1.grepToolDefinition);
        this.definitions.set('glob', glob_1.globToolDefinition);
        this.definitions.set('write', write_1.writeToolDefinition);
        this.definitions.set('edit', edit_1.editToolDefinition);
        this.definitions.set('web', web_1.webToolDefinition);
    }
    async executeTool(toolName, options) {
        const startTime = Date.now();
        if (!this.tools.has(toolName)) {
            return {
                success: false,
                output: '',
                error: `Unknown tool: ${toolName}`,
                toolName,
                executionTime: Date.now() - startTime,
                options
            };
        }
        try {
            const tool = this.tools.get(toolName);
            const result = await tool.execute(options);
            return {
                ...result,
                toolName,
                executionTime: Date.now() - startTime,
                options
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Tool execution failed: ${error instanceof Error ? error.message : String(error)}`,
                toolName,
                executionTime: Date.now() - startTime,
                options
            };
        }
    }
    getToolDefinitions() {
        return Array.from(this.definitions.values());
    }
    getToolDefinition(toolName) {
        return this.definitions.get(toolName);
    }
    getAvailableTools() {
        return Array.from(this.tools.keys());
    }
    validateToolCall(toolName, options) {
        const definition = this.definitions.get(toolName);
        if (!definition) {
            return { valid: false, error: `Unknown tool: ${toolName}` };
        }
        // Basic validation - check required parameters
        const required = definition.function.parameters.required || [];
        for (const param of required) {
            if (!(param in options) || options[param] === undefined || options[param] === null) {
                return { valid: false, error: `Missing required parameter: ${param}` };
            }
        }
        return { valid: true };
    }
}
exports.ToolManager = ToolManager;
//# sourceMappingURL=index.js.map