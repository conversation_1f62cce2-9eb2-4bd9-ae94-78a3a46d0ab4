export interface AIProvider {
    name: string;
    baseUrl: string;
    apiKey: string;
    models: string[];
    defaultModel: string;
    sendMessage(messages: AIMessage[], tools?: ToolDefinition[], model?: string, options?: any): Promise<AIMessage>;
    streamMessage?(messages: AIMessage[], tools?: ToolDefinition[], model?: string, onChunk?: (chunk: string) => void): Promise<AIMessage>;
}
export interface DeepseekProvider extends AIProvider {
    name: 'deepseek';
    models: ['deepseek-chat', 'deepseek-reasoner'];
}
export interface OllamaProvider extends AIProvider {
    name: 'ollama';
    baseUrl: string;
}
export interface AIMessage {
    role: 'system' | 'user' | 'assistant' | 'tool';
    content: string;
    toolCalls?: ToolCall[];
    toolCallId?: string;
}
export interface ToolCall {
    id: string;
    type: 'function';
    function: {
        name: string;
        arguments: string;
    };
}
export interface ToolDefinition {
    type: 'function';
    function: {
        name: string;
        description: string;
        parameters: {
            type: 'object';
            properties: Record<string, any>;
            required: string[];
        };
    };
}
export interface ToolResult {
    success: boolean;
    output: string;
    error?: string;
    metadata?: Record<string, any>;
}
export interface CLIConfig {
    provider: 'deepseek' | 'ollama';
    apiKey?: string;
    model: string;
    baseUrl?: string;
    maxRetries: number;
    retryDelay: number;
    timeout: number;
}
export interface ChatSession {
    id: string;
    messages: AIMessage[];
    createdAt: Date;
    updatedAt: Date;
}
export interface CommandContext {
    config: CLIConfig;
    session: ChatSession;
    workingDirectory: string;
    isInterrupted: boolean;
}
export interface SpinnerState {
    isActive: boolean;
    message: string;
    elapsedSeconds: number;
    frameIndex: number;
}
export interface UIComponent {
    render(): void;
    update(data: any): void;
    destroy(): void;
}
export interface SlashCommand {
    name: string;
    description: string;
    execute: (args: string[], config?: CLIConfig) => Promise<any>;
}
export interface RetryOptions {
    maxRetries: number;
    baseDelay: number;
    maxDelay: number;
    exponentialBackoff: boolean;
    retryCondition?: (error: Error) => boolean;
}
export interface ErrorContext {
    operation: string;
    timestamp: Date;
    error: Error;
    retryCount: number;
    context?: Record<string, any>;
}
export interface BashToolOptions {
    command: string;
    workingDirectory?: string;
    timeout?: number;
    env?: Record<string, string>;
}
export interface GrepToolOptions {
    pattern: string;
    paths?: string[];
    recursive?: boolean;
    ignoreCase?: boolean;
    maxResults?: number;
}
export interface GlobToolOptions {
    pattern: string;
    cwd?: string;
    ignore?: string[];
    maxResults?: number;
}
export interface WriteToolOptions {
    path: string;
    content: string;
    encoding?: string;
    createDirectories?: boolean;
}
export interface EditToolOptions {
    path: string;
    operation: 'replace' | 'insert' | 'delete' | 'create';
    content?: string;
    lineNumber?: number;
    searchPattern?: string;
    replaceWith?: string;
}
export interface WebToolOptions {
    query: string;
    maxResults?: number;
    timeout?: number;
}
export type ToolOptions = BashToolOptions | GrepToolOptions | GlobToolOptions | WriteToolOptions | EditToolOptions | WebToolOptions;
export interface ToolExecutionResult extends ToolResult {
    toolName: string;
    executionTime: number;
    options: ToolOptions;
}
//# sourceMappingURL=index.d.ts.map