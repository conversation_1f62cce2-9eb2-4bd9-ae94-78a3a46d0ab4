{"version": 3, "file": "edit.js", "sourceRoot": "", "sources": ["../../../src/core/tools/edit.ts"], "names": [], "mappings": ";;;;;;AACA,2BAAoC;AACpC,gDAAwB;AAEX,QAAA,kBAAkB,GAAmB;IAChD,IAAI,EAAE,UAAU;IAChB,QAAQ,EAAE;QACR,IAAI,EAAE,MAAM;QACZ,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAmC6B;QAC1C,UAAU,EAAE;YACV,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,mBAAmB;iBACjC;gBACD,SAAS,EAAE;oBACT,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;oBAC/C,WAAW,EAAE,mCAAmC;iBACjD;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,wEAAwE;iBACtF;gBACD,UAAU,EAAE;oBACV,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,8DAA8D;iBAC5E;gBACD,aAAa,EAAE;oBACb,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,oEAAoE;iBAClF;gBACD,WAAW,EAAE;oBACX,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,yEAAyE;iBACvF;aACF;YACD,QAAQ,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC;SAChC;KACF;CACF,CAAC;AAEF,MAAa,QAAQ;IACnB,KAAK,CAAC,OAAO,CAAC,OAAwB;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;YAE9C,kBAAkB;YAClB,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC9C,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,EAAE;oBACV,KAAK,EAAE,2BAA2B;iBACnC,CAAC;YACJ,CAAC;YAED,MAAM,YAAY,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAE5C,QAAQ,SAAS,EAAE,CAAC;gBAClB,KAAK,QAAQ;oBACX,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;gBACjE,KAAK,SAAS;oBACZ,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;gBACrE,KAAK,QAAQ;oBACX,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;gBACpE,KAAK,QAAQ;oBACX,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;gBACpE;oBACE,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,MAAM,EAAE,EAAE;wBACV,KAAK,EAAE,sBAAsB,SAAS,EAAE;wBACxC,QAAQ,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE;qBACpD,CAAC;YACN,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,0BAA0B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACzF,QAAQ,EAAE;oBACR,QAAQ,EAAE,OAAO,CAAC,IAAI;oBACtB,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;iBACtC;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,QAAgB,EAAE,OAAwB,EAAE,SAAiB;QACpF,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;QAEjC,IAAI,CAAC;YACH,+BAA+B;YAC/B,IAAI,CAAC;gBACH,MAAM,aAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC1B,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,EAAE;oBACV,KAAK,EAAE,wBAAwB,QAAQ,EAAE;oBACzC,QAAQ,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE;iBACpD,CAAC;YACJ,CAAC;YAAC,MAAM,CAAC;gBACP,4CAA4C;YAC9C,CAAC;YAED,4BAA4B;YAC5B,MAAM,SAAS,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YACzC,MAAM,aAAE,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAE/C,cAAc;YACd,MAAM,aAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAE9C,MAAM,IAAI,GAAG,MAAM,aAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACrC,MAAM,YAAY,GAAG,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAC;YAE5D,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,iBAAiB,YAAY,KAAK,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;gBAC3E,QAAQ,EAAE;oBACR,QAAQ;oBACR,YAAY;oBACZ,SAAS,EAAE,QAAQ;oBACnB,QAAQ,EAAE,IAAI,CAAC,IAAI;oBACnB,YAAY,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM;oBACxC,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;iBACtC;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,0BAA0B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACzF,QAAQ,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE;aACpD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,QAAgB,EAAE,OAAwB,EAAE,SAAiB;QACxF,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QAExD,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,kDAAkD;gBACzD,QAAQ,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE;aACpD,CAAC;QACJ,CAAC;QAED,MAAM,WAAW,GAAG,WAAW,IAAI,OAAO,IAAI,EAAE,CAAC;QAEjD,IAAI,CAAC;YACH,YAAY;YACZ,MAAM,eAAe,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAE5D,sBAAsB;YACtB,MAAM,UAAU,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,aAAa,EAAE,GAAG,CAAC,EAAE,WAAW,CAAC,CAAC;YAExF,IAAI,UAAU,KAAK,eAAe,EAAE,CAAC;gBACnC,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE,iCAAiC,aAAa,EAAE;oBACxD,QAAQ,EAAE;wBACR,QAAQ;wBACR,SAAS,EAAE,SAAS;wBACpB,aAAa;wBACb,YAAY,EAAE,CAAC;wBACf,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;qBACtC;iBACF,CAAC;YACJ,CAAC;YAED,wBAAwB;YACxB,MAAM,aAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;YAEjD,MAAM,UAAU,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;YACxF,MAAM,YAAY,GAAG,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAC;YAE5D,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,YAAY,UAAU,sBAAsB,YAAY,EAAE;gBAClE,QAAQ,EAAE;oBACR,QAAQ;oBACR,YAAY;oBACZ,SAAS,EAAE,SAAS;oBACpB,aAAa;oBACb,WAAW;oBACX,YAAY,EAAE,UAAU;oBACxB,YAAY,EAAE,eAAe,CAAC,MAAM;oBACpC,OAAO,EAAE,UAAU,CAAC,MAAM;oBAC1B,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;iBACtC;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,8BAA8B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBAC7F,QAAQ,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE;aACpD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,QAAgB,EAAE,OAAwB,EAAE,SAAiB;QACvF,MAAM,EAAE,UAAU,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;QAE7C,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;YAC7B,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,8CAA8C;gBACrD,QAAQ,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE;aACpD,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,YAAY;YACZ,MAAM,eAAe,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC5D,MAAM,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAE1C,uBAAuB;YACvB,IAAI,UAAU,GAAG,CAAC,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpD,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,EAAE;oBACV,KAAK,EAAE,wBAAwB,UAAU,cAAc,KAAK,CAAC,MAAM,SAAS;oBAC5E,QAAQ,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE;iBACpD,CAAC;YACJ,CAAC;YAED,iBAAiB;YACjB,MAAM,WAAW,GAAG,UAAU,GAAG,CAAC,CAAC;YACnC,KAAK,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;YAEtC,wBAAwB;YACxB,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,MAAM,aAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;YAEjD,MAAM,YAAY,GAAG,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAC;YAE5D,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,4BAA4B,UAAU,QAAQ,YAAY,EAAE;gBACpE,QAAQ,EAAE;oBACR,QAAQ;oBACR,YAAY;oBACZ,SAAS,EAAE,QAAQ;oBACnB,UAAU;oBACV,eAAe,EAAE,OAAO;oBACxB,aAAa,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC;oBAC/B,QAAQ,EAAE,KAAK,CAAC,MAAM;oBACtB,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;iBACtC;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBAC5F,QAAQ,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE;aACpD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,QAAgB,EAAE,OAAwB,EAAE,SAAiB;QACvF,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;QAE9C,IAAI,CAAC,UAAU,IAAI,CAAC,aAAa,EAAE,CAAC;YAClC,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,uEAAuE;gBAC9E,QAAQ,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE;aACpD,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YACH,YAAY;YACZ,MAAM,eAAe,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC5D,IAAI,UAAkB,CAAC;YACvB,IAAI,YAAY,GAAG,CAAC,CAAC;YAErB,IAAI,UAAU,EAAE,CAAC;gBACf,uBAAuB;gBACvB,MAAM,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAE1C,IAAI,UAAU,GAAG,CAAC,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;oBAChD,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,MAAM,EAAE,EAAE;wBACV,KAAK,EAAE,wBAAwB,UAAU,cAAc,KAAK,CAAC,MAAM,SAAS;wBAC5E,QAAQ,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE;qBACpD,CAAC;gBACJ,CAAC;gBAED,KAAK,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;gBAChC,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC9B,YAAY,GAAG,CAAC,CAAC;YAEnB,CAAC;iBAAM,IAAI,aAAa,EAAE,CAAC;gBACzB,gCAAgC;gBAChC,MAAM,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAC1C,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,aAAa,CAAC,CAAC;gBACxC,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;gBAE9D,YAAY,GAAG,KAAK,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;gBACnD,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;YACpD,CAAC;YAED,IAAI,YAAY,KAAK,CAAC,EAAE,CAAC;gBACvB,OAAO;oBACL,OAAO,EAAE,IAAI;oBACb,MAAM,EAAE,aAAa;wBACnB,CAAC,CAAC,oCAAoC,aAAa,EAAE;wBACrD,CAAC,CAAC,oBAAoB;oBACxB,QAAQ,EAAE;wBACR,QAAQ;wBACR,SAAS,EAAE,QAAQ;wBACnB,YAAY,EAAE,CAAC;wBACf,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;qBACtC;iBACF,CAAC;YACJ,CAAC;YAED,wBAAwB;YACxB,MAAM,aAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;YAEjD,MAAM,YAAY,GAAG,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAC;YAC5D,MAAM,WAAW,GAAG,UAAU;gBAC5B,CAAC,CAAC,QAAQ,UAAU,EAAE;gBACtB,CAAC,CAAC,GAAG,YAAY,2BAA2B,CAAC;YAE/C,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,WAAW,WAAW,UAAU,YAAY,EAAE;gBACtD,QAAQ,EAAE;oBACR,QAAQ;oBACR,YAAY;oBACZ,SAAS,EAAE,QAAQ;oBACnB,UAAU;oBACV,aAAa;oBACb,YAAY;oBACZ,YAAY,EAAE,eAAe,CAAC,MAAM;oBACpC,OAAO,EAAE,UAAU,CAAC,MAAM;oBAC1B,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;iBACtC;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,6BAA6B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBAC5F,QAAQ,EAAE,EAAE,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,EAAE;aACpD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,KAAa;QAClC,IAAI,KAAK,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QAE9B,MAAM,CAAC,GAAG,IAAI,CAAC;QACf,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACtC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpD,OAAO,UAAU,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;CACF;AA9UD,4BA8UC"}