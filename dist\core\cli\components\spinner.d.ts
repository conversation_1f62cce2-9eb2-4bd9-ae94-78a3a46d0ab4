import { UIComponent } from '../../../types';
export declare class SpinnerComponent implements UIComponent {
    private state;
    private intervalId;
    private startTime;
    private readonly ballFrames;
    start(message: string): void;
    stop(): void;
    update(data: any): void;
    render(): void;
    destroy(): void;
    private clearLine;
    private formatElapsedTime;
    setMessage(message: string): void;
    getMessage(): string;
    getElapsedTime(): number;
    isActive(): boolean;
    static start(message: string): SpinnerComponent;
    succeed(message?: string): void;
    fail(message?: string): void;
    warn(message?: string): void;
    info(message?: string): void;
}
export declare const globalSpinner: SpinnerComponent;
export declare function startSpinner(message: string): SpinnerComponent;
export declare function stopSpinner(): void;
export declare function updateSpinner(message: string): void;
//# sourceMappingURL=spinner.d.ts.map