import { ToolDefinition, ToolResult, ToolOptions, ToolExecutionResult } from '../../types';
import { BashTool, bashToolDefinition } from './bash';
import { GrepTool, grepToolDefinition } from './grep';
import { GlobTool, globToolDefinition } from './glob';
import { WriteTool, writeToolDefinition } from './write';
import { EditTool, editToolDefinition } from './edit';
import { WebTool, webToolDefinition } from './web';
export declare class ToolManager {
    private tools;
    private definitions;
    constructor();
    private initializeTools;
    executeTool(toolName: string, options: ToolOptions): Promise<ToolExecutionResult>;
    getToolDefinitions(): ToolDefinition[];
    getToolDefinition(toolName: string): ToolDefinition | undefined;
    getAvailableTools(): string[];
    validateToolCall(toolName: string, options: any): {
        valid: boolean;
        error?: string;
    };
}
export { BashTool, GrepTool, GlobTool, WriteTool, EditTool, WebTool, bashToolDefinition, grepToolDefinition, globToolDefinition, writeToolDefinition, editToolDefinition, webToolDefinition };
export type { ToolDefinition, ToolResult, ToolOptions, ToolExecutionResult };
//# sourceMappingURL=index.d.ts.map