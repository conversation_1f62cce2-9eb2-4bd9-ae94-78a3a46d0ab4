import { <PERSON><PERSON><PERSON>ider, AIMessage, ToolDefinition } from '../../../types';
export interface OllamaResponse {
    model: string;
    created_at: string;
    message: {
        role: string;
        content: string;
        tool_calls?: Array<{
            function: {
                name: string;
                arguments: Record<string, any>;
            };
        }>;
    };
    done: boolean;
    total_duration?: number;
    load_duration?: number;
    prompt_eval_count?: number;
    prompt_eval_duration?: number;
    eval_count?: number;
    eval_duration?: number;
}
export interface OllamaModel {
    name: string;
    model: string;
    modified_at: string;
    size: number;
    digest: string;
    details: {
        parent_model: string;
        format: string;
        family: string;
        families: string[];
        parameter_size: string;
        quantization_level: string;
    };
}
export declare class OllamaProvider implements AIProvider {
    readonly baseUrl: string;
    readonly apiKey: string;
    readonly name = "ollama";
    readonly models: string[];
    defaultModel: string;
    constructor(baseUrl?: string, apiKey?: string);
    initialize(): Promise<void>;
    loadAvailableModels(): Promise<void>;
    sendMessage(messages: AIMessage[], tools?: ToolDefinition[], model?: string, options?: {
        temperature?: number;
        maxTokens?: number;
        stream?: boolean;
    }): Promise<AIMessage>;
    streamMessage(messages: AIMessage[], tools?: ToolDefinition[], model?: string, onChunk?: (chunk: string) => void): Promise<AIMessage>;
    pullModel(modelName: string): Promise<void>;
    validateModel(model: string): boolean;
    getAvailableModels(): string[];
    checkConnection(): Promise<boolean>;
}
//# sourceMappingURL=ollama.d.ts.map