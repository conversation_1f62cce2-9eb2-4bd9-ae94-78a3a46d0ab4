{"version": 3, "file": "agent.js", "sourceRoot": "", "sources": ["../../../src/core/ai/agent.ts"], "names": [], "mappings": ";;;AAOA,mDAAwD;AACxD,+CAAoD;AACpD,oCAAuC;AACvC,mDAAgD;AAChD,iDAAuD;AACvD,0CAA8C;AAG9C,MAAa,OAAO;IACV,QAAQ,GAAsB,IAAI,CAAC;IACnC,WAAW,CAAc;IACzB,aAAa,CAAgB;IAC7B,YAAY,CAAe;IAEnC;QACE,IAAI,CAAC,WAAW,GAAG,IAAI,mBAAW,EAAE,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,IAAI,oBAAa,EAAE,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,IAAI,oBAAY,EAAE,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,OAAuB;QACtC,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAE3B,yBAAyB;QACzB,IAAI,MAAM,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;YACnC,IAAI,CAAC,QAAQ,GAAG,IAAI,2BAAgB,CAAC,MAAM,CAAC,MAAO,CAAC,CAAC;QACvD,CAAC;aAAM,IAAI,MAAM,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACxC,IAAI,CAAC,QAAQ,GAAG,IAAI,uBAAc,CAAC,MAAM,CAAC,OAAQ,CAAC,CAAC;YACpD,MAAO,IAAI,CAAC,QAA2B,CAAC,UAAU,EAAE,CAAC;QACvD,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,yBAAyB,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,WAAmB,EAAE,OAAuB;QAC/D,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QAED,8BAA8B;QAC9B,MAAM,OAAO,GAAc,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC;QAClE,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEvC,uBAAuB;QACvB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAE3C,oCAAoC;QACpC,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,CACtC,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EACjC;YACE,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,UAAU;YACrC,SAAS,EAAE,OAAO,CAAC,MAAM,CAAC,UAAU;YACpC,QAAQ,EAAE,KAAK;YACf,kBAAkB,EAAE,IAAI;YACxB,cAAc,EAAE,CAAC,KAAY,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;SAC1D,EACD,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;YACjB,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAC5F,CAAC,CACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,OAAuB;QACjD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QAED,yBAAyB;QACzB,IAAI,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAE,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,sCAAsC;QACtC,MAAM,QAAQ,GAAgB;YAC5B,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,6BAAa,EAAE;YAC1C,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ;SAC5B,CAAC;QAEF,uBAAuB;QACvB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC;QAEpD,0BAA0B;QAC1B,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;QAErD,IAAI,CAAC;YACH,iCAAiC;YACjC,IAAI,UAAqB,CAAC;YAE1B,4DAA4D;YAC5D,IAAI,IAAI,CAAC,QAAQ,EAAE,aAAa,EAAE,CAAC;gBACjC,IAAI,CAAC;oBACH,4CAA4C;oBAC5C,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;oBAClC,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;oBAEpC,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,CAC5C,QAAQ,EACR,KAAK,EACL,OAAO,CAAC,MAAM,CAAC,KAAK,EACpB,CAAC,KAAa,EAAE,EAAE;wBAChB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAE,EAAE,CAAC;4BACjD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;wBACxC,CAAC;oBACH,CAAC,CACF,CAAC;oBACF,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;gBACpC,CAAC;gBAAC,OAAO,WAAW,EAAE,CAAC;oBACrB,+CAA+C;oBAC/C,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;oBAClC,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC;oBACrD,UAAU,GAAG,MAAM,IAAI,CAAC,QAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBACrF,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;oBAClC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;gBAChD,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,+EAA+E;gBAC/E,UAAU,GAAG,MAAM,IAAI,CAAC,QAAS,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACrF,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;gBAClC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAChD,CAAC;YAED,6BAA6B;YAC7B,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAE1C,6BAA6B;YAC7B,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5D,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACzD,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;YAClC,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,SAAqB,EAAE,OAAuB;QACvE,MAAM,WAAW,GAAgB,EAAE,CAAC;QAEpC,wBAAwB;QACxB,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzB,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,aAAa,SAAS,CAAC,MAAM,WAAW,CAAC,CAAC;QAC3E,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAE9B,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,SAAS;YACX,CAAC;YAED,yBAAyB;YACzB,IAAI,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAE,EAAE,CAAC;gBAChD,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,oCAAoC,CAAC,CAAC;gBACxE,MAAM;YACR,CAAC;YAED,mCAAmC;YACnC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzB,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;YACnG,CAAC;YAED,IAAI,CAAC;gBACH,uBAAuB;gBACvB,IAAI,IAAS,CAAC;gBACd,IAAI,CAAC;oBACH,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;gBACjD,CAAC;gBAAC,OAAO,UAAU,EAAE,CAAC;oBACpB,MAAM,QAAQ,GAAG,8BAA8B,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBACvF,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;oBAE1C,WAAW,CAAC,IAAI,CAAC;wBACf,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,UAAU,QAAQ,EAAE;wBAC7B,UAAU,EAAE,QAAQ,CAAC,EAAE;qBACxB,CAAC,CAAC;oBACH,SAAS;gBACX,CAAC;gBAED,qBAAqB;gBACrB,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACnF,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;oBACtB,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,UAAU,CAAC,KAAM,EAAE,SAAS,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;oBAEtF,WAAW,CAAC,IAAI,CAAC;wBACf,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,UAAU,UAAU,CAAC,KAAK,EAAE;wBACrC,UAAU,EAAE,QAAQ,CAAC,EAAE;qBACxB,CAAC,CAAC;oBACH,SAAS;gBACX,CAAC;gBAED,+BAA+B;gBAC/B,IAAI,CAAC,aAAa,CAAC,oBAAoB,CACrC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EACtB,OAAO,EACP,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAC1B,CAAC;gBAEF,gCAAgC;gBAChC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,CACrD,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,EAChE;oBACE,UAAU,EAAE,CAAC,EAAE,0BAA0B;oBACzC,SAAS,EAAE,IAAI;oBACf,QAAQ,EAAE,IAAI;oBACd,kBAAkB,EAAE,IAAI;oBACxB,cAAc,EAAE,CAAC,KAAY,EAAE,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;iBAC9D,EACD,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;oBACjB,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC,EAAE,QAAQ,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;gBACzG,CAAC,CACF,CAAC;gBAEF,gCAAgC;gBAChC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,IAAI,CAAC,aAAa,CAAC,oBAAoB,CACrC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EACtB,SAAS,EACT,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAC9B,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,aAAa,CAAC,oBAAoB,CACrC,QAAQ,CAAC,QAAQ,CAAC,IAAI,EACtB,OAAO,EACP,MAAM,CAAC,KAAK,CACb,CAAC;gBACJ,CAAC;gBAED,8BAA8B;gBAC9B,WAAW,CAAC,IAAI,CAAC;oBACf,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,UAAU,MAAM,CAAC,KAAK,EAAE;oBAClE,UAAU,EAAE,QAAQ,CAAC,EAAE;iBACxB,CAAC,CAAC;YAEL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,QAAQ,GAAG,0BAA0B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;gBACpG,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,QAAQ,EAAE,SAAS,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;gBAE7E,WAAW,CAAC,IAAI,CAAC;oBACf,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,UAAU,QAAQ,EAAE;oBAC7B,UAAU,EAAE,QAAQ,CAAC,EAAE;iBACxB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,8BAA8B;QAC9B,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;QAE9C,0DAA0D;QAC1D,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,sBAAsB,EAAE,EAAE,CAAC;YAC3E,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,KAAY;QAC9B,MAAM,eAAe,GAAG;YACtB,YAAY;YACZ,WAAW;YACX,cAAc;YACd,WAAW;YACX,YAAY;YACZ,SAAS;YACT,SAAS;YACT,WAAW;SACZ,CAAC;QAEF,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QACjD,OAAO,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;IAC7E,CAAC;IAEO,eAAe,CAAC,KAAY;QAClC,MAAM,mBAAmB,GAAG;YAC1B,YAAY;YACZ,WAAW;YACX,WAAW;YACX,MAAM;SACP,CAAC;QAEF,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;QACjD,OAAO,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;IACjF,CAAC;IAEO,cAAc,CAAC,IAAS;QAC9B,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;iBACnC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,KAAK,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;iBAC1G,IAAI,CAAC,IAAI,CAAC,CAAC;YAEd,OAAO,SAAS,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;QAC9E,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,mBAAmB,CAAC;QAC7B,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,MAA2B;QAClD,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACzB,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,aAAa,IAAI,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YACpB,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBAChC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;YACpE,CAAC;YACD,IAAI,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;gBACrC,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAC1D,CAAC;YACD,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE,CAAC;gBAC9C,KAAK,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAEO,cAAc,CAAC,KAAa;QAClC,IAAI,KAAK,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QAE9B,MAAM,CAAC,GAAG,IAAI,CAAC;QACf,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACtC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpD,OAAO,UAAU,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED,qCAAqC;IACrC,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAC;QAClC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;IAChC,CAAC;CACF;AArUD,0BAqUC"}