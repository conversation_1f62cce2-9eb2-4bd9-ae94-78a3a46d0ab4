import { ToolDefinition, ToolResult, WebToolOptions } from '../../types';
export declare const webToolDefinition: ToolDefinition;
export declare class WebTool {
    private readonly baseUrl;
    private readonly userAgent;
    execute(options: WebToolOptions): Promise<ToolResult>;
    private searchDuckDuckGo;
    private fallbackSearch;
    private extractTitleFromText;
    private formatSearchResults;
    validateQuery(query: string): {
        valid: boolean;
        error?: string;
    };
    suggestQueryImprovements(query: string): string[];
}
//# sourceMappingURL=web.d.ts.map