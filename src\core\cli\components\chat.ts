import inquirer from 'inquirer';
import { AIMessage, CommandContext } from '@/types';
import chalk from 'chalk';
import { SpinnerComponent } from './spinner';

export class ChatComponent {
  private isInterrupted = false;
  private currentSpinner: SpinnerComponent | null = null;

  constructor() {
    this.setupInterruptHandler();
  }

  async getInput(prompt: string = '💬 You: '): Promise<string> {
    this.isInterrupted = false;

    const answer = await inquirer.prompt([
      {
        type: 'input',
        name: 'message',
        message: prompt,
        validate: (input: string) => {
          if (input.trim().length === 0) {
            return 'Please enter a message';
          }
          return true;
        }
      }
    ]);

    return answer.message.trim();
  }

  displayMessage(message: AIMessage, config?: { streaming?: boolean }): void {
    const { role, content } = message;
    
    switch (role) {
      case 'user':
        console.log(chalk.blue(`💬 You: ${content}`));
        break;
      
      case 'assistant':
        if (config?.streaming) {
          // For streaming, content is displayed as it comes
          process.stdout.write(chalk.green(content));
        } else {
          console.log(chalk.green(`🤖 Arien: ${content}`));
        }
        break;
      
      case 'system':
        console.log(chalk.gray(`⚙️  System: ${content}`));
        break;
      
      case 'tool':
        console.log(chalk.yellow(`🔧 Tool: ${content}`));
        break;
      
      default:
        console.log(chalk.white(content));
    }
  }

  displayToolExecution(toolName: string, status: 'start' | 'success' | 'error', details?: string): void {
    switch (status) {
      case 'start':
        console.log(chalk.cyan(`🔧 Executing ${toolName}...`));
        if (details) {
          console.log(chalk.gray(`   ${details}`));
        }
        break;
      
      case 'success':
        console.log(chalk.green(`✅ ${toolName} completed successfully`));
        if (details) {
          console.log(chalk.gray(`   ${details}`));
        }
        break;
      
      case 'error':
        console.log(chalk.red(`❌ ${toolName} failed`));
        if (details) {
          console.log(chalk.red(`   ${details}`));
        }
        break;
    }
  }

  startStreaming(initialMessage?: string): void {
    if (initialMessage) {
      process.stdout.write(chalk.green(`🤖 Arien: ${initialMessage}`));
    } else {
      process.stdout.write(chalk.green('🤖 Arien: '));
    }
  }

  streamChunk(chunk: string): void {
    process.stdout.write(chalk.green(chunk));
  }

  endStreaming(): void {
    console.log(); // New line after streaming
  }

  showThinking(message: string): SpinnerComponent {
    this.currentSpinner = new SpinnerComponent();
    this.currentSpinner.start(message);
    return this.currentSpinner;
  }

  hideThinking(): void {
    if (this.currentSpinner) {
      this.currentSpinner.stop();
      this.currentSpinner = null;
    }
  }

  displayError(error: string, context?: string): void {
    console.log(chalk.red(`❌ Error: ${error}`));
    if (context) {
      console.log(chalk.gray(`   Context: ${context}`));
    }
  }

  displayWarning(warning: string): void {
    console.log(chalk.yellow(`⚠️  Warning: ${warning}`));
  }

  displayInfo(info: string): void {
    console.log(chalk.blue(`ℹ️  ${info}`));
  }

  displaySuccess(message: string): void {
    console.log(chalk.green(`✅ ${message}`));
  }

  displaySeparator(): void {
    console.log(chalk.gray('─'.repeat(60)));
  }

  displaySessionInfo(context: CommandContext): void {
    const { config, session } = context;
    console.log(chalk.gray(`📊 Session: ${session.id.slice(0, 8)} | Provider: ${config.provider} | Model: ${config.model}`));
  }

  displayRetryAttempt(attempt: number, maxRetries: number, reason: string): void {
    console.log(chalk.yellow(`🔄 Retry ${attempt}/${maxRetries}: ${reason}`));
  }

  displayProgress(current: number, total: number, operation: string): void {
    const percentage = Math.round((current / total) * 100);
    const progressBar = '█'.repeat(Math.floor(percentage / 5)) + '░'.repeat(20 - Math.floor(percentage / 5));
    
    process.stdout.write(`\r🔄 ${operation}: [${progressBar}] ${percentage}%`);
    
    if (current === total) {
      console.log(); // New line when complete
    }
  }

  clearLine(): void {
    process.stdout.write('\r\x1b[K');
  }

  private setupInterruptHandler(): void {
    let escapeCount = 0;
    let lastEscapeTime = 0;

    // Only set up raw mode if stdin is a TTY
    if (process.stdin.isTTY) {
      process.stdin.setRawMode(true);
      process.stdin.resume();
      process.stdin.setEncoding('utf8');

      process.stdin.on('data', (key: string) => {
        // Check for double ESC press
        if (key === '\u001b') { // ESC key
          const now = Date.now();

          if (now - lastEscapeTime < 500) { // Within 500ms
            escapeCount++;
            if (escapeCount >= 2) {
              this.handleInterrupt();
              escapeCount = 0;
            }
          } else {
            escapeCount = 1;
          }

          lastEscapeTime = now;
        } else {
          escapeCount = 0;
        }

        // Handle Ctrl+C
        if (key === '\u0003') {
          this.handleInterrupt();
        }
      });
    }

    // Also handle SIGINT for non-TTY environments
    process.on('SIGINT', () => {
      this.handleInterrupt();
    });
  }

  private handleInterrupt(): void {
    this.isInterrupted = true;
    
    if (this.currentSpinner) {
      this.currentSpinner.stop();
    }
    
    console.log(chalk.red('\n🛑 Operation interrupted by user'));
    
    // Reset terminal state
    process.stdout.write('\r\x1b[K');
  }

  isOperationInterrupted(): boolean {
    return this.isInterrupted;
  }

  resetInterruptState(): void {
    this.isInterrupted = false;
  }

  // Utility method for formatted output
  formatOutput(content: string, options: {
    maxWidth?: number;
    indent?: number;
    prefix?: string;
  } = {}): string {
    const { maxWidth = 80, indent = 0, prefix = '' } = options;
    const indentStr = ' '.repeat(indent);
    const effectiveWidth = maxWidth - indent - prefix.length;
    
    const lines = content.split('\n');
    const wrappedLines: string[] = [];
    
    for (const line of lines) {
      if (line.length <= effectiveWidth) {
        wrappedLines.push(indentStr + prefix + line);
      } else {
        // Simple word wrapping
        const words = line.split(' ');
        let currentLine = '';
        
        for (const word of words) {
          if ((currentLine + word).length <= effectiveWidth) {
            currentLine += (currentLine ? ' ' : '') + word;
          } else {
            if (currentLine) {
              wrappedLines.push(indentStr + prefix + currentLine);
            }
            currentLine = word;
          }
        }
        
        if (currentLine) {
          wrappedLines.push(indentStr + prefix + currentLine);
        }
      }
    }
    
    return wrappedLines.join('\n');
  }

  // Method to display help information
  displayHelp(): void {
    console.log(chalk.cyan.bold('\n📖 Arien AI CLI Help\n'));
    
    console.log(chalk.white('Basic Usage:'));
    console.log(chalk.gray('  • Type your message and press Enter to chat with AI'));
    console.log(chalk.gray('  • The AI can execute tools to help accomplish tasks'));
    console.log(chalk.gray('  • Double-tap ESC to interrupt ongoing operations\n'));
    
    console.log(chalk.white('Slash Commands:'));
    console.log(chalk.gray('  • /help     - Show this help message'));
    console.log(chalk.gray('  • /provider - Change AI provider settings'));
    console.log(chalk.gray('  • /model    - Change AI model'));
    console.log(chalk.gray('  • /clear    - Clear chat history'));
    console.log(chalk.gray('  • /exit     - Exit the application\n'));
    
    console.log(chalk.white('Available Tools:'));
    console.log(chalk.gray('  • bash      - Execute shell commands'));
    console.log(chalk.gray('  • grep      - Search file contents'));
    console.log(chalk.gray('  • glob      - Find files by pattern'));
    console.log(chalk.gray('  • write     - Create/update files'));
    console.log(chalk.gray('  • edit      - Edit existing files'));
    console.log(chalk.gray('  • web       - Search the internet\n'));
  }
}
