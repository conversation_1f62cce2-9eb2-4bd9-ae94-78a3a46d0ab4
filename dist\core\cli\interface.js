"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CLIInterface = void 0;
const login_1 = require("./components/login");
const chat_1 = require("./components/chat");
const slash_commands_1 = require("./components/slash-commands");
const agent_1 = require("../../core/ai/agent");
const config_1 = require("../../config");
const chalk_1 = __importDefault(require("chalk"));
const crypto_1 = require("crypto");
class CLIInterface {
    loginComponent;
    chatComponent;
    slashCommands;
    aiAgent;
    context = null;
    constructor() {
        this.loginComponent = new login_1.LoginComponent();
        this.chatComponent = new chat_1.ChatComponent();
        this.slashCommands = new slash_commands_1.SlashCommandsComponent();
        this.aiAgent = new agent_1.AIAgent();
    }
    async start() {
        try {
            console.clear();
            this.displayBanner();
            // Initialize configuration
            await this.initializeConfiguration();
            // Initialize AI agent
            await this.aiAgent.initialize(this.context);
            // Display welcome message
            this.loginComponent.displayWelcome();
            // Start interactive session
            await this.runInteractiveSession();
        }
        catch (error) {
            console.log(chalk_1.default.red(`❌ Failed to start CLI: ${error instanceof Error ? error.message : String(error)}`));
            process.exit(1);
        }
    }
    displayBanner() {
        const banner = `
╔═══════════════════════════════════════════════════════════════╗
║                                                               ║
║     █████╗ ██████╗ ██╗███████╗███╗   ██╗     █████╗ ██╗      ║
║    ██╔══██╗██╔══██╗██║██╔════╝████╗  ██║    ██╔══██╗██║      ║
║    ███████║██████╔╝██║█████╗  ██╔██╗ ██║    ███████║██║      ║
║    ██╔══██║██╔══██╗██║██╔══╝  ██║╚██╗██║    ██╔══██║██║      ║
║    ██║  ██║██║  ██║██║███████╗██║ ╚████║    ██║  ██║██║      ║
║    ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═══╝    ╚═╝  ╚═╝╚═╝      ║
║                                                               ║
║           🤖 Local Agentic CLI Terminal System 🤖            ║
║                     Powered by AI v1.0.0                     ║
║                                                               ║
╚═══════════════════════════════════════════════════════════════╝
    `;
        console.log(chalk_1.default.cyan(banner));
    }
    async initializeConfiguration() {
        let config;
        // Check if configuration exists
        if (config_1.configManager.exists()) {
            config = config_1.configManager.get();
            // Validate existing configuration
            const validation = config_1.configManager.isValid();
            if (!validation.valid) {
                console.log(chalk_1.default.yellow('⚠️  Configuration issues found:'));
                validation.errors.forEach(error => console.log(chalk_1.default.red(`   • ${error}`)));
                console.log(chalk_1.default.blue('🔧 Let\'s reconfigure...\n'));
                config = await this.loginComponent.configure();
                config_1.configManager.set(config);
            }
            else {
                console.log(chalk_1.default.green(`✅ Using existing configuration (${config.provider}/${config.model})\n`));
            }
        }
        else {
            // First time setup
            config = await this.loginComponent.configure();
            config_1.configManager.set(config);
        }
        // Create command context
        this.context = {
            config,
            session: this.createNewSession(),
            workingDirectory: process.cwd(),
            isInterrupted: false
        };
    }
    createNewSession() {
        return {
            id: (0, crypto_1.randomUUID)(),
            messages: [],
            createdAt: new Date(),
            updatedAt: new Date()
        };
    }
    async runInteractiveSession() {
        console.log(chalk_1.default.gray('Type your message, use "/" for commands, or "exit" to quit.\n'));
        while (true) {
            try {
                // Reset interrupt state
                this.chatComponent.resetInterruptState();
                // Display session info
                this.chatComponent.displaySessionInfo(this.context);
                // Get user input
                const input = await this.chatComponent.getInput();
                // Handle exit commands
                if (this.isExitCommand(input)) {
                    console.log(chalk_1.default.green('👋 Goodbye!'));
                    break;
                }
                // Handle slash commands or show picker if just "/"
                if (input.startsWith('/')) {
                    let commandInput = input;
                    if (input.trim() === '/') {
                        // Show command picker
                        const selectedCommand = await this.slashCommands.showCommandPicker();
                        if (selectedCommand) {
                            commandInput = `/${selectedCommand}`;
                        }
                        else {
                            continue; // User cancelled
                        }
                    }
                    const result = await this.slashCommands.handleSlashCommand(commandInput, this.context.config);
                    if (result.shouldExit) {
                        break;
                    }
                    if (result.shouldClear) {
                        this.context.session = this.createNewSession();
                        console.clear();
                        this.displayBanner();
                        continue;
                    }
                    if (result.newConfig) {
                        this.context.config = result.newConfig;
                        config_1.configManager.set(result.newConfig);
                        await this.aiAgent.initialize(this.context);
                    }
                    continue;
                }
                // Handle help command
                if (input.toLowerCase() === 'help') {
                    this.chatComponent.displayHelp();
                    continue;
                }
                // Process message with AI
                await this.aiAgent.processMessage(input, this.context);
                // Update session timestamp
                this.context.session.updatedAt = new Date();
                // Add separator for readability
                this.chatComponent.displaySeparator();
            }
            catch (error) {
                if (error instanceof Error && error.message.includes('interrupted')) {
                    console.log(chalk_1.default.yellow('🛑 Operation interrupted'));
                }
                else {
                    this.chatComponent.displayError(error instanceof Error ? error.message : String(error), 'Interactive session');
                }
                // Add separator after errors
                this.chatComponent.displaySeparator();
            }
        }
        // Cleanup
        await this.shutdown();
    }
    isExitCommand(input) {
        const exitCommands = ['exit', 'quit', 'bye', 'goodbye'];
        return exitCommands.includes(input.toLowerCase().trim());
    }
    async shutdown() {
        try {
            await this.aiAgent.shutdown();
            console.log(chalk_1.default.gray('🔄 Session saved'));
        }
        catch (error) {
            console.log(chalk_1.default.yellow(`⚠️  Shutdown warning: ${error instanceof Error ? error.message : String(error)}`));
        }
    }
    // Method to handle graceful shutdown on signals
    setupGracefulShutdown() {
        const handleShutdown = async (signal) => {
            console.log(chalk_1.default.yellow(`\n🛑 Received ${signal}, shutting down gracefully...`));
            await this.shutdown();
            process.exit(0);
        };
        process.on('SIGINT', () => handleShutdown('SIGINT'));
        process.on('SIGTERM', () => handleShutdown('SIGTERM'));
        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            console.log(chalk_1.default.red(`💥 Uncaught exception: ${error.message}`));
            console.log(chalk_1.default.gray(error.stack));
            process.exit(1);
        });
        process.on('unhandledRejection', (reason) => {
            console.log(chalk_1.default.red(`💥 Unhandled rejection: ${reason}`));
            process.exit(1);
        });
    }
    // Static method to create and start CLI
    static async run() {
        const cli = new CLIInterface();
        cli.setupGracefulShutdown();
        await cli.start();
    }
}
exports.CLIInterface = CLIInterface;
//# sourceMappingURL=interface.js.map