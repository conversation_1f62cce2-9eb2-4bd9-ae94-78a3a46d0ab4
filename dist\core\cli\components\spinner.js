"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.globalSpinner = exports.SpinnerComponent = void 0;
exports.startSpinner = startSpinner;
exports.stopSpinner = stopSpinner;
exports.updateSpinner = updateSpinner;
class SpinnerComponent {
    state = {
        isActive: false,
        message: '',
        elapsedSeconds: 0,
        frameIndex: 0
    };
    intervalId = null;
    startTime = 0;
    ballFrames = [
        "( ●    )",
        "(  ●   )",
        "(   ●  )",
        "(    ● )",
        "(     ●)",
        "(    ● )",
        "(   ●  )",
        "(  ●   )",
        "( ●    )",
        "(●     )",
    ];
    start(message) {
        if (this.state.isActive) {
            this.stop();
        }
        this.state = {
            isActive: true,
            message,
            elapsedSeconds: 0,
            frameIndex: 0
        };
        this.startTime = Date.now();
        this.render();
        // Update every 100ms for smooth animation
        this.intervalId = setInterval(() => {
            this.update({});
        }, 100);
    }
    stop() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
        this.state.isActive = false;
        this.clearLine();
    }
    update(data) {
        if (!this.state.isActive)
            return;
        // Update elapsed time
        this.state.elapsedSeconds = Math.floor((Date.now() - this.startTime) / 1000);
        // Update frame index for animation
        this.state.frameIndex = (this.state.frameIndex + 1) % this.ballFrames.length;
        // Update message if provided
        if (data.message) {
            this.state.message = data.message;
        }
        this.render();
    }
    render() {
        if (!this.state.isActive)
            return;
        const frame = this.ballFrames[this.state.frameIndex];
        const elapsed = this.formatElapsedTime(this.state.elapsedSeconds);
        const message = this.state.message || 'Processing...';
        // Clear current line and render new content
        process.stdout.write('\r\x1b[K'); // Clear line
        process.stdout.write(`${frame} ${message} ${elapsed}`);
    }
    destroy() {
        this.stop();
    }
    clearLine() {
        process.stdout.write('\r\x1b[K'); // Clear current line
    }
    formatElapsedTime(seconds) {
        if (seconds < 60) {
            return `(${seconds}s)`;
        }
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `(${minutes}m ${remainingSeconds}s)`;
    }
    // Utility methods for different spinner states
    setMessage(message) {
        this.state.message = message;
    }
    getMessage() {
        return this.state.message;
    }
    getElapsedTime() {
        return this.state.elapsedSeconds;
    }
    isActive() {
        return this.state.isActive;
    }
    // Static method to create and start a spinner
    static start(message) {
        const spinner = new SpinnerComponent();
        spinner.start(message);
        return spinner;
    }
    // Method to update with success message and stop
    succeed(message) {
        if (message) {
            this.state.message = message;
        }
        this.clearLine();
        console.log(`✅ ${this.state.message}`);
        this.stop();
    }
    // Method to update with error message and stop
    fail(message) {
        if (message) {
            this.state.message = message;
        }
        this.clearLine();
        console.log(`❌ ${this.state.message}`);
        this.stop();
    }
    // Method to update with warning message and stop
    warn(message) {
        if (message) {
            this.state.message = message;
        }
        this.clearLine();
        console.log(`⚠️  ${this.state.message}`);
        this.stop();
    }
    // Method to update with info message and stop
    info(message) {
        if (message) {
            this.state.message = message;
        }
        this.clearLine();
        console.log(`ℹ️  ${this.state.message}`);
        this.stop();
    }
}
exports.SpinnerComponent = SpinnerComponent;
// Export a singleton instance for global use
exports.globalSpinner = new SpinnerComponent();
// Utility functions for quick spinner operations
function startSpinner(message) {
    return SpinnerComponent.start(message);
}
function stopSpinner() {
    exports.globalSpinner.stop();
}
function updateSpinner(message) {
    exports.globalSpinner.setMessage(message);
}
//# sourceMappingURL=spinner.js.map