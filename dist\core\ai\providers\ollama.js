"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OllamaProvider = void 0;
const axios_1 = __importDefault(require("axios"));
class OllamaProvider {
    baseUrl;
    apiKey;
    name = 'ollama';
    models = [];
    defaultModel = 'llama3.2';
    constructor(baseUrl = 'http://localhost:11434', apiKey = '' // Ollama doesn't require API key by default
    ) {
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
    }
    async initialize() {
        try {
            await this.loadAvailableModels();
        }
        catch (error) {
            throw new Error(`Failed to connect to Ollama at ${this.baseUrl}. Make sure Ollama is running.`);
        }
    }
    async loadAvailableModels() {
        try {
            const response = await axios_1.default.get(`${this.baseUrl}/api/tags`, { timeout: 5000 });
            this.models.length = 0; // Clear existing models
            this.models.push(...response.data.models.map(model => model.name));
            if (this.models.length > 0 && !this.models.includes(this.defaultModel)) {
                this.defaultModel = this.models[0] || 'llama3.2';
            }
        }
        catch (error) {
            if (axios_1.default.isAxiosError(error)) {
                if (error.code === 'ECONNREFUSED') {
                    throw new Error('Cannot connect to Ollama. Please ensure Ollama is running on ' + this.baseUrl);
                }
                throw new Error(`Ollama connection error: ${error.message}`);
            }
            throw error;
        }
    }
    async sendMessage(messages, tools, model = this.defaultModel, options = {}) {
        try {
            const requestBody = {
                model,
                messages: messages.map(msg => ({
                    role: msg.role,
                    content: msg.content,
                    tool_calls: msg.toolCalls
                })),
                tools: tools?.map(tool => tool.function),
                options: {
                    temperature: options.temperature ?? 0.7,
                    num_predict: options.maxTokens ?? 4000
                },
                stream: false
            };
            const response = await axios_1.default.post(`${this.baseUrl}/api/chat`, requestBody, {
                headers: {
                    'Content-Type': 'application/json'
                },
                timeout: 60000
            });
            const message = {
                role: 'assistant',
                content: response.data.message.content,
                toolCalls: response.data.message.tool_calls?.map(tc => ({
                    id: `call_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                    type: 'function',
                    function: {
                        name: tc.function.name,
                        arguments: JSON.stringify(tc.function.arguments)
                    }
                }))
            };
            return message;
        }
        catch (error) {
            if (axios_1.default.isAxiosError(error)) {
                const status = error.response?.status;
                const message = error.response?.data?.error || error.message;
                if (status === 404) {
                    throw new Error(`Model '${model}' not found. Available models: ${this.models.join(', ')}`);
                }
                else if (error.code === 'ECONNREFUSED') {
                    throw new Error('Cannot connect to Ollama. Please ensure Ollama is running.');
                }
                else {
                    throw new Error(`Ollama API error: ${message}`);
                }
            }
            throw error;
        }
    }
    async streamMessage(messages, tools, model = this.defaultModel, onChunk) {
        try {
            const requestBody = {
                model,
                messages: messages.map(msg => ({
                    role: msg.role,
                    content: msg.content,
                    tool_calls: msg.toolCalls
                })),
                tools: tools?.map(tool => tool.function),
                options: {
                    temperature: 0.7,
                    num_predict: 4000
                },
                stream: true
            };
            const response = await axios_1.default.post(`${this.baseUrl}/api/chat`, requestBody, {
                headers: {
                    'Content-Type': 'application/json'
                },
                responseType: 'stream',
                timeout: 120000
            });
            let fullContent = '';
            let toolCalls = [];
            return new Promise((resolve, reject) => {
                response.data.on('data', (chunk) => {
                    const lines = chunk.toString().split('\n').filter(line => line.trim());
                    for (const line of lines) {
                        try {
                            const parsed = JSON.parse(line);
                            if (parsed.message?.content) {
                                fullContent += parsed.message.content;
                                onChunk?.(parsed.message.content);
                            }
                            if (parsed.message?.tool_calls) {
                                for (const tc of parsed.message.tool_calls) {
                                    toolCalls.push({
                                        id: `call_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                                        type: 'function',
                                        function: {
                                            name: tc.function.name,
                                            arguments: JSON.stringify(tc.function.arguments)
                                        }
                                    });
                                }
                            }
                            if (parsed.done) {
                                resolve({
                                    role: 'assistant',
                                    content: fullContent,
                                    toolCalls: toolCalls.length > 0 ? toolCalls : undefined
                                });
                                return;
                            }
                        }
                        catch (parseError) {
                            // Ignore parsing errors for incomplete chunks
                        }
                    }
                });
                response.data.on('error', (error) => {
                    reject(error);
                });
                response.data.on('end', () => {
                    resolve({
                        role: 'assistant',
                        content: fullContent,
                        toolCalls: toolCalls.length > 0 ? toolCalls : undefined
                    });
                });
            });
        }
        catch (error) {
            if (axios_1.default.isAxiosError(error)) {
                const message = error.response?.data?.error || error.message;
                throw new Error(`Ollama streaming error: ${message}`);
            }
            throw error;
        }
    }
    async pullModel(modelName) {
        try {
            await axios_1.default.post(`${this.baseUrl}/api/pull`, { name: modelName }, {
                headers: { 'Content-Type': 'application/json' },
                timeout: 300000 // 5 minutes for model download
            });
            // Refresh available models after pulling
            await this.loadAvailableModels();
        }
        catch (error) {
            if (axios_1.default.isAxiosError(error)) {
                throw new Error(`Failed to pull model '${modelName}': ${error.message}`);
            }
            throw error;
        }
    }
    validateModel(model) {
        return this.models.includes(model);
    }
    getAvailableModels() {
        return [...this.models];
    }
    async checkConnection() {
        try {
            await axios_1.default.get(`${this.baseUrl}/api/tags`, { timeout: 3000 });
            return true;
        }
        catch {
            return false;
        }
    }
}
exports.OllamaProvider = OllamaProvider;
//# sourceMappingURL=ollama.js.map