import { Command<PERSON>ontext, ChatSession, CLIConfig } from '@/types';
import { LoginComponent } from './components/login';
import { ChatComponent } from './components/chat';
import { SlashCommandsComponent } from './components/slash-commands';
import { AIAgent } from '@/core/ai/agent';
import { configManager } from '@/config';
import chalk from 'chalk';
import { randomUUID } from 'crypto';

export class CLIInterface {
  private loginComponent: LoginComponent;
  private chatComponent: ChatComponent;
  private slashCommands: SlashCommandsComponent;
  private aiAgent: AIAgent;
  private context: CommandContext | null = null;

  constructor() {
    this.loginComponent = new LoginComponent();
    this.chatComponent = new ChatComponent();
    this.slashCommands = new SlashCommandsComponent();
    this.aiAgent = new AIAgent();
  }

  async start(): Promise<void> {
    try {
      console.clear();
      this.displayBanner();

      // Initialize configuration
      await this.initializeConfiguration();

      // Initialize AI agent
      await this.aiAgent.initialize(this.context!);

      // Display welcome message
      this.loginComponent.displayWelcome();

      // Start interactive session
      await this.runInteractiveSession();

    } catch (error) {
      console.log(chalk.red(`❌ Failed to start CLI: ${error instanceof Error ? error.message : String(error)}`));
      process.exit(1);
    }
  }

  private displayBanner(): void {
    const banner = `
╔═══════════════════════════════════════════════════════════════╗
║                                                               ║
║     █████╗ ██████╗ ██╗███████╗███╗   ██╗     █████╗ ██╗      ║
║    ██╔══██╗██╔══██╗██║██╔════╝████╗  ██║    ██╔══██╗██║      ║
║    ███████║██████╔╝██║█████╗  ██╔██╗ ██║    ███████║██║      ║
║    ██╔══██║██╔══██╗██║██╔══╝  ██║╚██╗██║    ██╔══██║██║      ║
║    ██║  ██║██║  ██║██║███████╗██║ ╚████║    ██║  ██║██║      ║
║    ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚══════╝╚═╝  ╚═══╝    ╚═╝  ╚═╝╚═╝      ║
║                                                               ║
║           🤖 Local Agentic CLI Terminal System 🤖            ║
║                     Powered by AI v1.0.0                     ║
║                                                               ║
╚═══════════════════════════════════════════════════════════════╝
    `;
    
    console.log(chalk.cyan(banner));
  }

  private async initializeConfiguration(): Promise<void> {
    let config: CLIConfig;

    // Check if configuration exists
    if (configManager.exists()) {
      config = configManager.get();
      
      // Validate existing configuration
      const validation = configManager.isValid();
      if (!validation.valid) {
        console.log(chalk.yellow('⚠️  Configuration issues found:'));
        validation.errors.forEach(error => console.log(chalk.red(`   • ${error}`)));
        console.log(chalk.blue('🔧 Let\'s reconfigure...\n'));
        
        config = await this.loginComponent.configure();
        configManager.set(config);
      } else {
        console.log(chalk.green(`✅ Using existing configuration (${config.provider}/${config.model})\n`));
      }
    } else {
      // First time setup
      config = await this.loginComponent.configure();
      configManager.set(config);
    }

    // Create command context
    this.context = {
      config,
      session: this.createNewSession(),
      workingDirectory: process.cwd(),
      isInterrupted: false
    };
  }

  private createNewSession(): ChatSession {
    return {
      id: randomUUID(),
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  private async runInteractiveSession(): Promise<void> {
    console.log(chalk.gray('Type your message, use "/" for commands, or "exit" to quit.\n'));

    while (true) {
      try {
        // Reset interrupt state
        this.chatComponent.resetInterruptState();

        // Display session info
        this.chatComponent.displaySessionInfo(this.context!);

        // Get user input
        const input = await this.chatComponent.getInput();

        // Handle exit commands
        if (this.isExitCommand(input)) {
          console.log(chalk.green('👋 Goodbye!'));
          break;
        }

        // Handle slash commands or show picker if just "/"
        if (input.startsWith('/')) {
          let commandInput = input;

          if (input.trim() === '/') {
            // Show command picker
            const selectedCommand = await this.slashCommands.showCommandPicker();
            if (selectedCommand) {
              commandInput = `/${selectedCommand}`;
            } else {
              continue; // User cancelled
            }
          }

          const result = await this.slashCommands.handleSlashCommand(commandInput, this.context!.config);

          if (result.shouldExit) {
            break;
          }

          if (result.shouldClear) {
            this.context!.session = this.createNewSession();
            console.clear();
            this.displayBanner();
            continue;
          }

          if (result.newConfig) {
            this.context!.config = result.newConfig;
            configManager.set(result.newConfig);
            await this.aiAgent.initialize(this.context!);
          }

          continue;
        }

        // Handle help command
        if (input.toLowerCase() === 'help') {
          this.chatComponent.displayHelp();
          continue;
        }

        // Process message with AI
        await this.aiAgent.processMessage(input, this.context!);

        // Update session timestamp
        this.context!.session.updatedAt = new Date();

        // Add separator for readability
        this.chatComponent.displaySeparator();

      } catch (error) {
        if (error instanceof Error && error.message.includes('interrupted')) {
          console.log(chalk.yellow('🛑 Operation interrupted'));
        } else {
          this.chatComponent.displayError(
            error instanceof Error ? error.message : String(error),
            'Interactive session'
          );
        }
        
        // Add separator after errors
        this.chatComponent.displaySeparator();
      }
    }

    // Cleanup
    await this.shutdown();
  }

  private isExitCommand(input: string): boolean {
    const exitCommands = ['exit', 'quit', 'bye', 'goodbye'];
    return exitCommands.includes(input.toLowerCase().trim());
  }

  private async shutdown(): Promise<void> {
    try {
      await this.aiAgent.shutdown();
      console.log(chalk.gray('🔄 Session saved'));
    } catch (error) {
      console.log(chalk.yellow(`⚠️  Shutdown warning: ${error instanceof Error ? error.message : String(error)}`));
    }
  }

  // Method to handle graceful shutdown on signals
  setupGracefulShutdown(): void {
    const handleShutdown = async (signal: string) => {
      console.log(chalk.yellow(`\n🛑 Received ${signal}, shutting down gracefully...`));
      await this.shutdown();
      process.exit(0);
    };

    process.on('SIGINT', () => handleShutdown('SIGINT'));
    process.on('SIGTERM', () => handleShutdown('SIGTERM'));
    
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.log(chalk.red(`💥 Uncaught exception: ${error.message}`));
      console.log(chalk.gray(error.stack));
      process.exit(1);
    });

    process.on('unhandledRejection', (reason) => {
      console.log(chalk.red(`💥 Unhandled rejection: ${reason}`));
      process.exit(1);
    });
  }

  // Static method to create and start CLI
  static async run(): Promise<void> {
    const cli = new CLIInterface();
    cli.setupGracefulShutdown();
    await cli.start();
  }
}
