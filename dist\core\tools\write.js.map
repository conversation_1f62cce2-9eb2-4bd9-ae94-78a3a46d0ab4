{"version": 3, "file": "write.js", "sourceRoot": "", "sources": ["../../../src/core/tools/write.ts"], "names": [], "mappings": ";;;;;;AACA,2BAAoC;AACpC,gDAAwB;AAEX,QAAA,mBAAmB,GAAmB;IACjD,IAAI,EAAE,UAAU;IAChB,QAAQ,EAAE;QACR,IAAI,EAAE,OAAO;QACb,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qCA8BoB;QACjC,UAAU,EAAE;YACV,IAAI,EAAE,QAAQ;YACd,UAAU,EAAE;gBACV,IAAI,EAAE;oBACJ,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,2CAA2C;iBACzD;gBACD,OAAO,EAAE;oBACP,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,mCAAmC;iBACjD;gBACD,QAAQ,EAAE;oBACR,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,yDAAyD;oBACtE,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAC;iBAC9D;gBACD,iBAAiB,EAAE;oBACjB,IAAI,EAAE,SAAS;oBACf,WAAW,EAAE,6EAA6E;iBAC3F;aACF;YACD,QAAQ,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;SAC9B;KACF;CACF,CAAC;AAEF,MAAa,SAAS;IACpB,KAAK,CAAC,OAAO,CAAC,OAAyB;QACrC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,EACJ,IAAI,EAAE,QAAQ,EACd,OAAO,EACP,QAAQ,GAAG,MAAM,EACjB,iBAAiB,GAAG,IAAI,EACzB,GAAG,OAAO,CAAC;YAEZ,kBAAkB;YAClB,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC9C,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,EAAE;oBACV,KAAK,EAAE,2BAA2B;iBACnC,CAAC;YACJ,CAAC;YAED,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;gBAC9C,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,EAAE;oBACV,KAAK,EAAE,qCAAqC;iBAC7C,CAAC;YACJ,CAAC;YAED,sCAAsC;YACtC,MAAM,YAAY,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC5C,MAAM,SAAS,GAAG,cAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAE7C,yDAAyD;YACzD,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,EAAE,CAAC;gBACvC,OAAO;oBACL,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,EAAE;oBACV,KAAK,EAAE,iDAAiD;iBACzD,CAAC;YACJ,CAAC;YAED,sCAAsC;YACtC,IAAI,iBAAiB,EAAE,CAAC;gBACtB,IAAI,CAAC;oBACH,MAAM,aAAE,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBACjD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,MAAM,EAAE,EAAE;wBACV,KAAK,EAAE,8BAA8B,SAAS,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;qBAC5G,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,+DAA+D;YAC/D,IAAI,WAAW,GAAG,KAAK,CAAC;YACxB,IAAI,YAAY,GAAG,CAAC,CAAC;YACrB,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,MAAM,aAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBACzC,WAAW,GAAG,IAAI,CAAC;gBACnB,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC;YAC3B,CAAC;YAAC,MAAM,CAAC;gBACP,oCAAoC;YACtC,CAAC;YAED,iBAAiB;YACjB,MAAM,aAAE,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,QAA0B,EAAE,CAAC,CAAC;YAEpF,+BAA+B;YAC/B,MAAM,IAAI,GAAG,MAAM,aAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACzC,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE7C,yBAAyB;YACzB,MAAM,MAAM,GAAG,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;YACnD,MAAM,QAAQ,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACrD,MAAM,YAAY,GAAG,cAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,YAAY,CAAC,CAAC;YAEhE,IAAI,MAAM,GAAG,GAAG,MAAM,UAAU,YAAY,KAAK,QAAQ,GAAG,CAAC;YAE7D,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC;gBAC1C,MAAM,OAAO,GAAG,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;oBACrD,QAAQ,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC;gBAC1F,MAAM,IAAI,KAAK,OAAO,GAAG,CAAC;YAC5B,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,MAAM;gBACN,QAAQ,EAAE;oBACR,QAAQ,EAAE,YAAY;oBACtB,YAAY;oBACZ,QAAQ,EAAE,IAAI,CAAC,IAAI;oBACnB,QAAQ;oBACR,WAAW;oBACX,YAAY;oBACZ,YAAY,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM;oBACxC,aAAa;oBACb,SAAS;iBACV;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAE7C,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,8BAA8B;gBAC9B,IAAK,KAAa,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACrC,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,MAAM,EAAE,EAAE;wBACV,KAAK,EAAE,sCAAsC,OAAO,CAAC,IAAI,EAAE;wBAC3D,QAAQ,EAAE,EAAE,aAAa,EAAE;qBAC5B,CAAC;gBACJ,CAAC;gBAED,IAAK,KAAa,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACrC,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,MAAM,EAAE,EAAE;wBACV,KAAK,EAAE,6BAA6B,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBAChE,QAAQ,EAAE,EAAE,aAAa,EAAE;qBAC5B,CAAC;gBACJ,CAAC;gBAED,IAAK,KAAa,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACrC,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,MAAM,EAAE,EAAE;wBACV,KAAK,EAAE,yBAAyB;wBAChC,QAAQ,EAAE,EAAE,aAAa,EAAE;qBAC5B,CAAC;gBACJ,CAAC;gBAED,IAAK,KAAa,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACrC,OAAO;wBACL,OAAO,EAAE,KAAK;wBACd,MAAM,EAAE,EAAE;wBACV,KAAK,EAAE,oCAAoC,OAAO,CAAC,IAAI,EAAE;wBACzD,QAAQ,EAAE,EAAE,aAAa,EAAE;qBAC5B,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,EAAE;gBACV,KAAK,EAAE,yBAAyB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;gBACxF,QAAQ,EAAE;oBACR,QAAQ,EAAE,OAAO,CAAC,IAAI;oBACtB,aAAa;iBACd;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,QAAgB;QACtC,MAAM,SAAS,GAAG;YAChB,MAAM;YACN,MAAM;YACN,OAAO;YACP,UAAU;YACV,WAAW;YACX,OAAO;YACP,MAAM;YACN,OAAO;YACP,aAAa;YACb,mBAAmB;YACnB,yBAAyB;SAC1B,CAAC;QAEF,MAAM,cAAc,GAAG,cAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAEhD,OAAO,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;YACpC,MAAM,mBAAmB,GAAG,cAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YAC1D,OAAO,cAAc,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;QACxD,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,cAAc,CAAC,KAAa;QAClC,IAAI,KAAK,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QAE9B,MAAM,CAAC,GAAG,IAAI,CAAC;QACf,MAAM,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACtC,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpD,OAAO,UAAU,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED,4DAA4D;IAC5D,KAAK,CAAC,UAAU,CAAC,QAAgB;QAC/B,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAC5C,MAAM,IAAI,GAAG,MAAM,aAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAEzC,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;gBAClB,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;gBACjE,MAAM,UAAU,GAAG,GAAG,YAAY,WAAW,SAAS,EAAE,CAAC;gBAEzD,MAAM,aAAE,CAAC,QAAQ,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;gBAC5C,OAAO,UAAU,CAAC;YACpB,CAAC;QACH,CAAC;QAAC,MAAM,CAAC;YACP,2CAA2C;QAC7C,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,0CAA0C;IAC1C,eAAe,CAAC,OAAe,EAAE,UAAsD,EAAE;QACvF,MAAM,EAAE,OAAO,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC,CAAC,eAAe;QAElF,IAAI,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC/C,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC;QAC5D,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,OAAO,EAAE,CAAC;YACjD,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,0BAA0B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;QAC5F,CAAC;QAED,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACzB,CAAC;IAED,4DAA4D;IAC5D,gBAAgB,CAAC,OAAe;QAC9B,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAE/B,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACvD,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACpE,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC/F,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAChG,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACvD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AA3PD,8BA2PC"}