{"version": 3, "file": "ollama.js", "sourceRoot": "", "sources": ["../../../../src/core/ai/providers/ollama.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA6C;AAyC7C,MAAa,cAAc;IAMP;IACA;IANF,IAAI,GAAG,QAAQ,CAAC;IAChB,MAAM,GAAa,EAAE,CAAC;IAC/B,YAAY,GAAG,UAAU,CAAC;IAEjC,YACkB,UAAkB,wBAAwB,EAC1C,SAAiB,EAAE,CAAC,4CAA4C;;QADhE,YAAO,GAAP,OAAO,CAAmC;QAC1C,WAAM,GAAN,MAAM,CAAa;IAClC,CAAC;IAEJ,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,kCAAkC,IAAI,CAAC,OAAO,gCAAgC,CAAC,CAAC;QAClG,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,IAAI,CAAC;YACH,MAAM,QAAQ,GAA6C,MAAM,eAAK,CAAC,GAAG,CACxE,GAAG,IAAI,CAAC,OAAO,WAAW,EAC1B,EAAE,OAAO,EAAE,IAAI,EAAE,CAClB,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,wBAAwB;YAChD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;YAEnE,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;gBACvE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC;YACnD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,eAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9B,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;oBAClC,MAAM,IAAI,KAAK,CAAC,+DAA+D,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC;gBAClG,CAAC;gBACD,MAAM,IAAI,KAAK,CAAC,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC/D,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CACf,QAAqB,EACrB,KAAwB,EACxB,QAAgB,IAAI,CAAC,YAAY,EACjC,UAII,EAAE;QAEN,IAAI,CAAC;YACH,MAAM,WAAW,GAAG;gBAClB,KAAK;gBACL,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBAC7B,IAAI,EAAE,GAAG,CAAC,IAAI;oBACd,OAAO,EAAE,GAAG,CAAC,OAAO;oBACpB,UAAU,EAAE,GAAG,CAAC,SAAS;iBAC1B,CAAC,CAAC;gBACH,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACxC,OAAO,EAAE;oBACP,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,GAAG;oBACvC,WAAW,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI;iBACvC;gBACD,MAAM,EAAE,KAAK;aACd,CAAC;YAEF,MAAM,QAAQ,GAAkC,MAAM,eAAK,CAAC,IAAI,CAC9D,GAAG,IAAI,CAAC,OAAO,WAAW,EAC1B,WAAW,EACX;gBACE,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,OAAO,EAAE,KAAK;aACf,CACF,CAAC;YAEF,MAAM,OAAO,GAAc;gBACzB,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO;gBACtC,SAAS,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBACtD,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;oBACvE,IAAI,EAAE,UAAmB;oBACzB,QAAQ,EAAE;wBACR,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI;wBACtB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;qBACjD;iBACF,CAAC,CAAC;aACJ,CAAC;YAEF,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,eAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9B,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,EAAE,MAAM,CAAC;gBACtC,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC;gBAE7D,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;oBACnB,MAAM,IAAI,KAAK,CAAC,UAAU,KAAK,kCAAkC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC7F,CAAC;qBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;oBACzC,MAAM,IAAI,KAAK,CAAC,4DAA4D,CAAC,CAAC;gBAChF,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,qBAAqB,OAAO,EAAE,CAAC,CAAC;gBAClD,CAAC;YACH,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CACjB,QAAqB,EACrB,KAAwB,EACxB,QAAgB,IAAI,CAAC,YAAY,EACjC,OAAiC;QAEjC,IAAI,CAAC;YACH,MAAM,WAAW,GAAG;gBAClB,KAAK;gBACL,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBAC7B,IAAI,EAAE,GAAG,CAAC,IAAI;oBACd,OAAO,EAAE,GAAG,CAAC,OAAO;oBACpB,UAAU,EAAE,GAAG,CAAC,SAAS;iBAC1B,CAAC,CAAC;gBACH,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACxC,OAAO,EAAE;oBACP,WAAW,EAAE,GAAG;oBAChB,WAAW,EAAE,IAAI;iBAClB;gBACD,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAC/B,GAAG,IAAI,CAAC,OAAO,WAAW,EAC1B,WAAW,EACX;gBACE,OAAO,EAAE;oBACP,cAAc,EAAE,kBAAkB;iBACnC;gBACD,YAAY,EAAE,QAAQ;gBACtB,OAAO,EAAE,MAAM;aAChB,CACF,CAAC;YAEF,IAAI,WAAW,GAAG,EAAE,CAAC;YACrB,IAAI,SAAS,GAAe,EAAE,CAAC;YAE/B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACrC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,KAAa,EAAE,EAAE;oBACzC,MAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;oBAEvE,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;wBACzB,IAAI,CAAC;4BACH,MAAM,MAAM,GAAmB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;4BAEhD,IAAI,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;gCAC5B,WAAW,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC;gCACtC,OAAO,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;4BACpC,CAAC;4BAED,IAAI,MAAM,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC;gCAC/B,KAAK,MAAM,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;oCAC3C,SAAS,CAAC,IAAI,CAAC;wCACb,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;wCACvE,IAAI,EAAE,UAAU;wCAChB,QAAQ,EAAE;4CACR,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI;4CACtB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;yCACjD;qCACF,CAAC,CAAC;gCACL,CAAC;4BACH,CAAC;4BAED,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;gCAChB,OAAO,CAAC;oCACN,IAAI,EAAE,WAAW;oCACjB,OAAO,EAAE,WAAW;oCACpB,SAAS,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;iCACxD,CAAC,CAAC;gCACH,OAAO;4BACT,CAAC;wBACH,CAAC;wBAAC,OAAO,UAAU,EAAE,CAAC;4BACpB,8CAA8C;wBAChD,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;oBACzC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAChB,CAAC,CAAC,CAAC;gBAEH,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;oBAC3B,OAAO,CAAC;wBACN,IAAI,EAAE,WAAW;wBACjB,OAAO,EAAE,WAAW;wBACpB,SAAS,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;qBACxD,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,eAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9B,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC;gBAC7D,MAAM,IAAI,KAAK,CAAC,2BAA2B,OAAO,EAAE,CAAC,CAAC;YACxD,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,SAAiB;QAC/B,IAAI,CAAC;YACH,MAAM,eAAK,CAAC,IAAI,CACd,GAAG,IAAI,CAAC,OAAO,WAAW,EAC1B,EAAE,IAAI,EAAE,SAAS,EAAE,EACnB;gBACE,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,OAAO,EAAE,MAAM,CAAC,+BAA+B;aAChD,CACF,CAAC;YAEF,yCAAyC;YACzC,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,eAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,yBAAyB,SAAS,MAAM,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC3E,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,aAAa,CAAC,KAAa;QACzB,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED,kBAAkB;QAChB,OAAO,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,IAAI,CAAC;YACH,MAAM,eAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,WAAW,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAC/D,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AArPD,wCAqPC"}