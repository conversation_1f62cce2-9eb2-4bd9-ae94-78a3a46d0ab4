import inquirer from 'inquirer';
import { CLIConfig, SlashCommand } from '@/types';
import { LoginComponent } from './login';
import { DeepseekProvider } from '@/core/ai/providers/deepseek';
import { OllamaProvider } from '@/core/ai/providers/ollama';
import chalk from 'chalk';

export class SlashCommandsComponent {
  private commands: Map<string, SlashCommand> = new Map();

  constructor() {
    this.initializeCommands();
  }

  private initializeCommands(): void {
    this.commands.set('help', {
      name: 'help',
      description: 'Show help information',
      execute: this.executeHelp.bind(this)
    });

    this.commands.set('provider', {
      name: 'provider',
      description: 'Change AI provider settings',
      execute: this.executeProvider.bind(this)
    });

    this.commands.set('model', {
      name: 'model',
      description: 'Change AI model',
      execute: this.executeModel.bind(this)
    });

    this.commands.set('clear', {
      name: 'clear',
      description: 'Clear chat history',
      execute: this.executeClear.bind(this)
    });

    this.commands.set('exit', {
      name: 'exit',
      description: 'Exit the application',
      execute: this.executeExit.bind(this)
    });

    this.commands.set('status', {
      name: 'status',
      description: 'Show current configuration status',
      execute: this.executeStatus.bind(this)
    });

    this.commands.set('tools', {
      name: 'tools',
      description: 'List available tools',
      execute: this.executeTools.bind(this)
    });
  }

  async handleSlashCommand(input: string, config: CLIConfig): Promise<{ 
    handled: boolean; 
    newConfig?: CLIConfig; 
    shouldExit?: boolean; 
    shouldClear?: boolean;
  }> {
    const trimmed = input.trim();
    
    if (!trimmed.startsWith('/')) {
      return { handled: false };
    }

    const parts = trimmed.slice(1).split(' ');
    const commandName = parts[0]?.toLowerCase();
    const args = parts.slice(1);

    if (!commandName) {
      console.log(chalk.red('❌ No command specified'));
      return { handled: true };
    }

    const command = this.commands.get(commandName);
    if (!command) {
      console.log(chalk.red(`❌ Unknown command: /${commandName}`));
      console.log(chalk.gray('Type /help to see available commands'));
      return { handled: true };
    }

    try {
      const result = await command.execute(args, config);
      return { handled: true, ...result };
    } catch (error) {
      console.log(chalk.red(`❌ Command failed: ${error instanceof Error ? error.message : String(error)}`));
      return { handled: true };
    }
  }

  async showCommandPicker(): Promise<string | null> {
    const choices = Array.from(this.commands.values()).map(cmd => ({
      name: `/${cmd.name} - ${cmd.description}`,
      value: cmd.name
    }));

    const answer = await inquirer.prompt([
      {
        type: 'list',
        name: 'command',
        message: 'Select a command:',
        choices: [
          ...choices,
          { name: 'Cancel', value: null }
        ]
      }
    ]);

    return answer.command;
  }

  private async executeHelp(_args: string[]): Promise<any> {
    console.log(chalk.cyan.bold('\n📖 Available Slash Commands\n'));
    
    for (const [name, command] of this.commands) {
      console.log(chalk.white(`/${name}`) + chalk.gray(` - ${command.description}`));
    }
    
    console.log(chalk.gray('\nTip: Type "/" and press Tab for command completion\n'));
    return {};
  }

  private async executeProvider(_args: string[], _config?: CLIConfig): Promise<any> {
    console.log(chalk.blue('🔧 Reconfiguring AI Provider...\n'));
    
    const loginComponent = new LoginComponent();
    const newConfig = await loginComponent.configure();
    
    return { newConfig };
  }

  private async executeModel(_args: string[], config?: CLIConfig): Promise<any> {
    if (!config) {
      console.log(chalk.red('❌ No configuration available'));
      return {};
    }

    console.log(chalk.blue('🔧 Changing AI Model...\n'));

    let availableModels: string[] = [];

    try {
      if (config.provider === 'deepseek') {
        availableModels = ['deepseek-chat', 'deepseek-reasoner'];
      } else if (config.provider === 'ollama') {
        const provider = new OllamaProvider(config.baseUrl!);
        await provider.initialize();
        availableModels = provider.getAvailableModels();
      }

      if (availableModels.length === 0) {
        console.log(chalk.yellow('⚠️  No models available'));
        return {};
      }

      const answer = await inquirer.prompt([
        {
          type: 'list',
          name: 'model',
          message: 'Select a model:',
          choices: availableModels.map(model => ({
            name: model === config.model ? `${model} (current)` : model,
            value: model
          }))
        }
      ]);

      if (answer.model === config.model) {
        console.log(chalk.gray('Model unchanged'));
        return {};
      }

      const newConfig = { ...config, model: answer.model };
      console.log(chalk.green(`✅ Model changed to: ${answer.model}`));
      
      return { newConfig };

    } catch (error) {
      console.log(chalk.red(`❌ Failed to change model: ${error instanceof Error ? error.message : String(error)}`));
      return {};
    }
  }

  private async executeClear(_args: string[]): Promise<any> {
    const answer = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: 'Are you sure you want to clear the chat history?',
        default: false
      }
    ]);

    if (answer.confirm) {
      console.log(chalk.green('✅ Chat history cleared'));
      return { shouldClear: true };
    }

    return {};
  }

  private async executeExit(_args: string[]): Promise<any> {
    const answer = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: 'Are you sure you want to exit?',
        default: true
      }
    ]);

    if (answer.confirm) {
      console.log(chalk.green('👋 Goodbye!'));
      return { shouldExit: true };
    }

    return {};
  }

  private async executeStatus(_args: string[], config?: CLIConfig): Promise<any> {
    if (!config) {
      console.log(chalk.red('❌ No configuration available'));
      return {};
    }

    console.log(chalk.cyan.bold('\n📊 Current Configuration\n'));

    console.log(chalk.white('Provider: ') + chalk.green(config.provider));
    console.log(chalk.white('Model: ') + chalk.green(config.model));
    
    if (config.baseUrl) {
      console.log(chalk.white('Base URL: ') + chalk.green(config.baseUrl));
    }
    
    console.log(chalk.white('Max Retries: ') + chalk.green(config.maxRetries.toString()));
    console.log(chalk.white('Timeout: ') + chalk.green(`${config.timeout}ms`));
    
    // Test connection
    console.log(chalk.blue('\n🔍 Testing connection...'));
    
    try {
      if (config.provider === 'deepseek') {
        const provider = new DeepseekProvider(config.apiKey!);
        await provider.sendMessage([
          { role: 'user', content: 'test' }
        ], undefined, config.model);
        console.log(chalk.green('✅ Connection successful'));
      } else if (config.provider === 'ollama') {
        const provider = new OllamaProvider(config.baseUrl!);
        const isConnected = await provider.checkConnection();
        
        if (isConnected) {
          await provider.initialize();
          console.log(chalk.green('✅ Connection successful'));
          
          const models = provider.getAvailableModels();
          console.log(chalk.white(`Available models: ${models.join(', ')}`));
        } else {
          console.log(chalk.red('❌ Connection failed'));
        }
      }
    } catch (error) {
      console.log(chalk.red(`❌ Connection failed: ${error instanceof Error ? error.message : String(error)}`));
    }
    
    console.log();
    return {};
  }

  private async executeTools(_args: string[]): Promise<any> {
    console.log(chalk.cyan.bold('\n🔧 Available Tools\n'));
    
    const tools = [
      { name: 'bash', description: 'Execute shell commands and system operations' },
      { name: 'grep', description: 'Search for text patterns in files' },
      { name: 'glob', description: 'Find files by name patterns' },
      { name: 'write', description: 'Create or overwrite files' },
      { name: 'edit', description: 'Edit existing files precisely' },
      { name: 'web', description: 'Search the internet for information' }
    ];
    
    for (const tool of tools) {
      console.log(chalk.white(`${tool.name}`) + chalk.gray(` - ${tool.description}`));
    }
    
    console.log(chalk.gray('\nThe AI will automatically choose and use these tools based on your requests.\n'));
    return {};
  }

  getAvailableCommands(): string[] {
    return Array.from(this.commands.keys());
  }

  getCommandDescription(commandName: string): string | undefined {
    return this.commands.get(commandName)?.description;
  }
}
