"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobTool = exports.globToolDefinition = void 0;
const fast_glob_1 = require("fast-glob");
const fs_1 = require("fs");
const path_1 = __importDefault(require("path"));
exports.globToolDefinition = {
    type: 'function',
    function: {
        name: 'glob',
        description: `Fast file pattern matching tool that finds files by name and pattern, returning matching paths sorted by modification time (newest first).

WHEN TO USE:
- Finding files by name patterns or extensions
- Listing directory contents with filtering
- Discovering files in project structures
- Locating configuration files, tests, or documentation
- File system exploration and analysis
- Building file lists for processing

WHEN NOT TO USE:
- Content-based searching (use grep tool instead)
- Single known file paths (use direct file operations)
- Complex content analysis

USAGE NOTES:
- Supports glob patterns: *, **, ?, [abc], {a,b,c}
- Can search recursively through directory trees
- Results sorted by modification time (newest first)
- Excludes common build/cache directories by default
- Supports both files and directories matching
- Cross-platform path handling

PATTERN EXAMPLES:
- "*.ts" - All TypeScript files in current directory
- "**/*.json" - All JSON files recursively
- "src/**/*.{ts,js}" - TypeScript and JavaScript files in src
- "test*" - Files/directories starting with "test"
- "**/*config*" - Files containing "config" in name
- "docs/**/*.md" - Markdown files in docs directory`,
        parameters: {
            type: 'object',
            properties: {
                pattern: {
                    type: 'string',
                    description: 'Glob pattern to match files and directories'
                },
                cwd: {
                    type: 'string',
                    description: 'Working directory for pattern matching (optional, defaults to current directory)'
                },
                ignore: {
                    type: 'array',
                    items: {
                        type: 'string'
                    },
                    description: 'Patterns to ignore (optional, has sensible defaults)'
                },
                maxResults: {
                    type: 'number',
                    description: 'Maximum number of results to return (optional, defaults to 100)'
                }
            },
            required: ['pattern']
        }
    }
};
class GlobTool {
    defaultIgnorePatterns = [
        '**/node_modules/**',
        '**/.git/**',
        '**/dist/**',
        '**/build/**',
        '**/coverage/**',
        '**/.next/**',
        '**/.nuxt/**',
        '**/target/**',
        '**/__pycache__/**',
        '**/*.pyc',
        '**/.DS_Store',
        '**/Thumbs.db',
        '**/.env',
        '**/.env.local',
        '**/.env.*.local',
        '**/npm-debug.log*',
        '**/yarn-debug.log*',
        '**/yarn-error.log*'
    ];
    async execute(options) {
        const startTime = Date.now();
        try {
            const { pattern, cwd = process.cwd(), ignore = [], maxResults = 100 } = options;
            // Validate pattern
            if (!pattern || pattern.trim().length === 0) {
                return {
                    success: false,
                    output: '',
                    error: 'Glob pattern cannot be empty'
                };
            }
            // Resolve working directory
            const resolvedCwd = path_1.default.resolve(cwd);
            // Check if working directory exists
            try {
                await fs_1.promises.access(resolvedCwd);
            }
            catch {
                return {
                    success: false,
                    output: '',
                    error: `Working directory does not exist: ${resolvedCwd}`
                };
            }
            // Combine ignore patterns
            const allIgnorePatterns = [...this.defaultIgnorePatterns, ...ignore];
            // Execute glob search
            const matches = await (0, fast_glob_1.glob)(pattern, {
                cwd: resolvedCwd,
                absolute: true,
                ignore: allIgnorePatterns,
                onlyFiles: false, // Include both files and directories
                markDirectories: true,
                stats: false // We'll get stats separately for better control
            });
            // Limit results
            const limitedMatches = matches.slice(0, maxResults);
            // Get detailed information for each match
            const results = [];
            for (const matchPath of limitedMatches) {
                try {
                    const stat = await fs_1.promises.stat(matchPath);
                    const relativePath = path_1.default.relative(resolvedCwd, matchPath);
                    const extension = path_1.default.extname(matchPath);
                    results.push({
                        path: matchPath,
                        relativePath,
                        isDirectory: stat.isDirectory(),
                        size: stat.size,
                        modifiedTime: stat.mtime,
                        extension
                    });
                }
                catch (error) {
                    // Skip files that can't be accessed
                    continue;
                }
            }
            // Sort by modification time (newest first)
            results.sort((a, b) => b.modifiedTime.getTime() - a.modifiedTime.getTime());
            // Format output
            const output = this.formatResults(results, pattern, resolvedCwd);
            const executionTime = Date.now() - startTime;
            return {
                success: true,
                output,
                metadata: {
                    pattern,
                    workingDirectory: resolvedCwd,
                    totalMatches: matches.length,
                    returnedResults: results.length,
                    truncated: matches.length > maxResults,
                    executionTime,
                    fileCount: results.filter(r => !r.isDirectory).length,
                    directoryCount: results.filter(r => r.isDirectory).length
                }
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Glob search failed: ${error instanceof Error ? error.message : String(error)}`,
                metadata: {
                    pattern: options.pattern,
                    executionTime: Date.now() - startTime
                }
            };
        }
    }
    formatResults(results, pattern, cwd) {
        if (results.length === 0) {
            return `No files or directories found matching pattern: ${pattern}`;
        }
        const output = [];
        const fileCount = results.filter(r => !r.isDirectory).length;
        const dirCount = results.filter(r => r.isDirectory).length;
        output.push(`Found ${results.length} matches for pattern: ${pattern}`);
        output.push(`Files: ${fileCount}, Directories: ${dirCount}`);
        output.push(`Working directory: ${cwd}\n`);
        // Group by type
        const directories = results.filter(r => r.isDirectory);
        const files = results.filter(r => !r.isDirectory);
        // Show directories first
        if (directories.length > 0) {
            output.push('📁 DIRECTORIES:');
            for (const dir of directories.slice(0, 20)) {
                const timeStr = dir.modifiedTime.toISOString().split('T')[0];
                output.push(`   ${dir.relativePath}/ (${timeStr})`);
            }
            if (directories.length > 20) {
                output.push(`   ... and ${directories.length - 20} more directories`);
            }
            output.push('');
        }
        // Show files
        if (files.length > 0) {
            output.push('📄 FILES:');
            // Group files by extension
            const filesByExt = new Map();
            for (const file of files) {
                const ext = file.extension || '(no extension)';
                if (!filesByExt.has(ext)) {
                    filesByExt.set(ext, []);
                }
                filesByExt.get(ext).push(file);
            }
            // Sort extensions by file count
            const sortedExtensions = Array.from(filesByExt.entries())
                .sort(([, a], [, b]) => b.length - a.length);
            for (const [ext, extFiles] of sortedExtensions) {
                if (extFiles.length > 1) {
                    output.push(`   ${ext} files (${extFiles.length}):`);
                }
                for (const file of extFiles.slice(0, 10)) {
                    const sizeStr = this.formatFileSize(file.size);
                    const timeStr = file.modifiedTime.toISOString().split('T')[0];
                    const prefix = extFiles.length > 1 ? '     ' : '   ';
                    output.push(`${prefix}${file.relativePath} (${sizeStr}, ${timeStr})`);
                }
                if (extFiles.length > 10) {
                    output.push(`     ... and ${extFiles.length - 10} more ${ext} files`);
                }
            }
        }
        return output.join('\n');
    }
    formatFileSize(bytes) {
        if (bytes === 0)
            return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }
    // Utility method to validate glob pattern
    validatePattern(pattern) {
        try {
            // Test the pattern with a simple glob call
            fast_glob_1.glob.sync(pattern, { cwd: process.cwd(), ignore: ['**/*'], absolute: false });
            return { valid: true };
        }
        catch (error) {
            return {
                valid: false,
                error: error instanceof Error ? error.message : 'Invalid glob pattern'
            };
        }
    }
    // Utility method to suggest patterns based on common use cases
    suggestPatterns(intent) {
        const suggestions = {
            'typescript': ['**/*.ts', '**/*.tsx', 'src/**/*.ts'],
            'javascript': ['**/*.js', '**/*.jsx', 'src/**/*.js'],
            'config': ['**/*config*', '**/*.config.*', '**/.*rc*'],
            'test': ['**/*.test.*', '**/*.spec.*', '**/test/**/*'],
            'docs': ['**/*.md', '**/docs/**/*', '**/*.txt'],
            'images': ['**/*.{png,jpg,jpeg,gif,svg}', '**/images/**/*'],
            'styles': ['**/*.{css,scss,sass,less}', '**/styles/**/*']
        };
        const lowerIntent = intent.toLowerCase();
        for (const [key, patterns] of Object.entries(suggestions)) {
            if (lowerIntent.includes(key)) {
                return patterns;
            }
        }
        return ['**/*']; // Default fallback
    }
}
exports.GlobTool = GlobTool;
//# sourceMappingURL=glob.js.map