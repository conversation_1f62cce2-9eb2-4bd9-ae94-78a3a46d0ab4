import { AIMessage, CommandContext } from '../../../types';
import { SpinnerComponent } from './spinner';
export declare class ChatComponent {
    private isInterrupted;
    private currentSpinner;
    constructor();
    getInput(prompt?: string): Promise<string>;
    displayMessage(message: AIMessage, config?: {
        streaming?: boolean;
    }): void;
    displayToolExecution(toolName: string, status: 'start' | 'success' | 'error', details?: string): void;
    startStreaming(initialMessage?: string): void;
    streamChunk(chunk: string): void;
    endStreaming(): void;
    showThinking(message: string): SpinnerComponent;
    hideThinking(): void;
    displayError(error: string, context?: string): void;
    displayWarning(warning: string): void;
    displayInfo(info: string): void;
    displaySuccess(message: string): void;
    displaySeparator(): void;
    displaySessionInfo(context: CommandContext): void;
    displayRetryAttempt(attempt: number, maxRetries: number, reason: string): void;
    displayProgress(current: number, total: number, operation: string): void;
    clearLine(): void;
    private setupInterruptHandler;
    private handleInterrupt;
    isOperationInterrupted(): boolean;
    resetInterruptState(): void;
    formatOutput(content: string, options?: {
        maxWidth?: number;
        indent?: number;
        prefix?: string;
    }): string;
    displayHelp(): void;
}
//# sourceMappingURL=chat.d.ts.map