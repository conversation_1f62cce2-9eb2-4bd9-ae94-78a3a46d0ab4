#!/usr/bin/env node
"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const interface_1 = require("./core/cli/interface");
const commander_1 = require("commander");
const chalk_1 = __importDefault(require("chalk"));
const config_1 = require("./config");
const program = new commander_1.Command();
program
    .name('arien')
    .description('Arien AI - Local Agentic CLI Terminal System powered by AI')
    .version('1.0.0');
program
    .command('start')
    .description('Start the interactive AI CLI session')
    .action(async () => {
    try {
        await interface_1.CLIInterface.run();
    }
    catch (error) {
        console.error(chalk_1.default.red(`Failed to start Arien AI: ${error instanceof Error ? error.message : String(error)}`));
        process.exit(1);
    }
});
program
    .command('config')
    .description('Manage configuration')
    .option('--show', 'Show current configuration')
    .option('--reset', 'Reset configuration to defaults')
    .option('--export <file>', 'Export configuration to file')
    .option('--import <file>', 'Import configuration from file')
    .action(async (options) => {
    try {
        if (options.show) {
            const config = config_1.configManager.get();
            console.log(chalk_1.default.cyan('📋 Current Configuration:'));
            console.log(JSON.stringify(config, null, 2));
        }
        else if (options.reset) {
            config_1.configManager.reset();
            console.log(chalk_1.default.green('✅ Configuration reset to defaults'));
        }
        else if (options.export) {
            const fs = await Promise.resolve().then(() => __importStar(require('fs/promises')));
            const exportData = config_1.configManager.export();
            await fs.writeFile(options.export, exportData, 'utf8');
            console.log(chalk_1.default.green(`✅ Configuration exported to ${options.export}`));
        }
        else if (options.import) {
            const fs = await Promise.resolve().then(() => __importStar(require('fs/promises')));
            const importData = await fs.readFile(options.import, 'utf8');
            config_1.configManager.import(importData);
            console.log(chalk_1.default.green(`✅ Configuration imported from ${options.import}`));
        }
        else {
            console.log(chalk_1.default.yellow('Please specify an option. Use --help for more information.'));
        }
    }
    catch (error) {
        console.error(chalk_1.default.red(`Configuration error: ${error instanceof Error ? error.message : String(error)}`));
        process.exit(1);
    }
});
program
    .command('doctor')
    .description('Check system health and configuration')
    .action(async () => {
    console.log(chalk_1.default.cyan('🔍 Running system diagnostics...\n'));
    try {
        // Check Node.js version
        const nodeVersion = process.version;
        console.log(chalk_1.default.white(`Node.js version: ${chalk_1.default.green(nodeVersion)}`));
        if (parseInt(nodeVersion.slice(1)) < 22) {
            console.log(chalk_1.default.yellow('⚠️  Node.js 22+ is recommended'));
        }
        else {
            console.log(chalk_1.default.green('✅ Node.js version is compatible'));
        }
        // Check configuration
        if (config_1.configManager.exists()) {
            const validation = config_1.configManager.isValid();
            if (validation.valid) {
                console.log(chalk_1.default.green('✅ Configuration is valid'));
                const config = config_1.configManager.get();
                console.log(chalk_1.default.white(`Provider: ${chalk_1.default.green(config.provider)}`));
                console.log(chalk_1.default.white(`Model: ${chalk_1.default.green(config.model)}`));
                // Test provider connection
                if (config.provider === 'deepseek') {
                    console.log(chalk_1.default.blue('🔍 Testing Deepseek connection...'));
                    const { DeepseekProvider } = await Promise.resolve().then(() => __importStar(require('./core/ai/providers/deepseek')));
                    try {
                        const provider = new DeepseekProvider(config.apiKey);
                        await provider.sendMessage([
                            { role: 'user', content: 'test' }
                        ], undefined, config.model);
                        console.log(chalk_1.default.green('✅ Deepseek connection successful'));
                    }
                    catch (error) {
                        console.log(chalk_1.default.red(`❌ Deepseek connection failed: ${error instanceof Error ? error.message : String(error)}`));
                    }
                }
                else if (config.provider === 'ollama') {
                    console.log(chalk_1.default.blue('🔍 Testing Ollama connection...'));
                    const { OllamaProvider } = await Promise.resolve().then(() => __importStar(require('./core/ai/providers/ollama')));
                    try {
                        const provider = new OllamaProvider(config.baseUrl);
                        const isConnected = await provider.checkConnection();
                        if (isConnected) {
                            console.log(chalk_1.default.green('✅ Ollama connection successful'));
                            await provider.initialize();
                            const models = provider.getAvailableModels();
                            console.log(chalk_1.default.white(`Available models: ${models.join(', ')}`));
                        }
                        else {
                            console.log(chalk_1.default.red('❌ Cannot connect to Ollama'));
                        }
                    }
                    catch (error) {
                        console.log(chalk_1.default.red(`❌ Ollama connection failed: ${error instanceof Error ? error.message : String(error)}`));
                    }
                }
            }
            else {
                console.log(chalk_1.default.red('❌ Configuration is invalid:'));
                validation.errors.forEach(error => console.log(chalk_1.default.red(`   • ${error}`)));
            }
        }
        else {
            console.log(chalk_1.default.yellow('⚠️  No configuration found. Run "arien start" to configure.'));
        }
        // Check dependencies
        console.log(chalk_1.default.blue('\n🔍 Checking dependencies...'));
        const packageJson = await Promise.resolve().then(() => __importStar(require('../package.json')));
        console.log(chalk_1.default.white(`Version: ${chalk_1.default.green(packageJson.version)}`));
        console.log(chalk_1.default.green('✅ All dependencies loaded successfully'));
        console.log(chalk_1.default.green('\n🎉 System check completed!'));
    }
    catch (error) {
        console.error(chalk_1.default.red(`System check failed: ${error instanceof Error ? error.message : String(error)}`));
        process.exit(1);
    }
});
// Default command (start interactive session)
program
    .action(async () => {
    try {
        await interface_1.CLIInterface.run();
    }
    catch (error) {
        console.error(chalk_1.default.red(`Failed to start Arien AI: ${error instanceof Error ? error.message : String(error)}`));
        process.exit(1);
    }
});
// Parse command line arguments
program.parse();
// If no command provided, show help
if (!process.argv.slice(2).length) {
    program.outputHelp();
}
//# sourceMappingURL=index.js.map