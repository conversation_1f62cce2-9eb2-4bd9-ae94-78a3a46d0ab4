import { AI<PERSON>rovider, AIMessage, ToolDefinition } from '../../../types';
export interface DeepseekResponse {
    id: string;
    object: string;
    created: number;
    model: string;
    choices: Array<{
        index: number;
        message: {
            role: string;
            content: string | null;
            tool_calls?: Array<{
                id: string;
                type: 'function';
                function: {
                    name: string;
                    arguments: string;
                };
            }>;
        };
        finish_reason: string;
    }>;
    usage: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
}
export declare class DeepseekProvider implements AIProvider {
    readonly apiKey: string;
    readonly name = "deepseek";
    readonly baseUrl = "https://api.deepseek.com/v1";
    readonly models: string[];
    readonly defaultModel = "deepseek-chat";
    constructor(apiKey: string);
    sendMessage(messages: AIMessage[], tools?: ToolDefinition[], model?: string, options?: {
        temperature?: number;
        maxTokens?: number;
        stream?: boolean;
    }): Promise<AIMessage>;
    streamMessage(messages: AIMessage[], tools?: ToolDefinition[], model?: string, onChunk?: (chunk: string) => void): Promise<AIMessage>;
    validateModel(model: string): boolean;
    getAvailableModels(): string[];
}
//# sourceMappingURL=deepseek.d.ts.map