"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrepTool = exports.grepToolDefinition = void 0;
const fs_1 = require("fs");
const path_1 = __importDefault(require("path"));
const fast_glob_1 = require("fast-glob");
exports.grepToolDefinition = {
    type: 'function',
    function: {
        name: 'grep',
        description: `Fast content search tool that finds files containing specific text or patterns, returning matching file paths sorted by modification time (newest first).

WHEN TO USE:
- Finding specific text content in files
- Searching for code patterns, functions, variables
- Locating configuration values or settings
- Finding error messages in log files
- Searching for imports, exports, or dependencies
- Code analysis and debugging

WHEN NOT TO USE:
- File listing by name/pattern (use glob tool instead)
- Simple file reading without search criteria
- Binary file searching (limited support)

USAGE NOTES:
- Supports regular expressions for complex pattern matching
- Can search recursively through directories
- Case-sensitive and case-insensitive options available
- Results are sorted by file modification time (newest first)
- Provides line numbers and context for matches
- Handles text files efficiently, limited binary file support

EXAMPLES:
- Search for "function.*export" in TypeScript files
- Find "error" or "ERROR" in log files (case-insensitive)
- Locate "import.*react" in JavaScript/TypeScript files
- Search for "TODO" or "FIXME" comments in source code
- Find configuration keys like "database.*url"
- Locate specific API endpoints or routes`,
        parameters: {
            type: 'object',
            properties: {
                pattern: {
                    type: 'string',
                    description: 'Text pattern or regular expression to search for'
                },
                paths: {
                    type: 'array',
                    items: {
                        type: 'string'
                    },
                    description: 'File paths or directories to search in (optional, defaults to current directory)'
                },
                recursive: {
                    type: 'boolean',
                    description: 'Search recursively through subdirectories (optional, defaults to true)'
                },
                ignoreCase: {
                    type: 'boolean',
                    description: 'Perform case-insensitive search (optional, defaults to false)'
                },
                maxResults: {
                    type: 'number',
                    description: 'Maximum number of matching files to return (optional, defaults to 50)'
                }
            },
            required: ['pattern']
        }
    }
};
class GrepTool {
    async execute(options) {
        const startTime = Date.now();
        try {
            const { pattern, paths = ['.'], recursive = true, ignoreCase = false, maxResults = 50 } = options;
            // Validate pattern
            if (!pattern || pattern.trim().length === 0) {
                return {
                    success: false,
                    output: '',
                    error: 'Search pattern cannot be empty'
                };
            }
            // Create regex from pattern
            let regex;
            try {
                const flags = ignoreCase ? 'gi' : 'g';
                regex = new RegExp(pattern, flags);
            }
            catch (error) {
                return {
                    success: false,
                    output: '',
                    error: `Invalid regular expression: ${error instanceof Error ? error.message : String(error)}`
                };
            }
            // Find files to search
            const filesToSearch = await this.findFilesToSearch(paths, recursive);
            if (filesToSearch.length === 0) {
                return {
                    success: true,
                    output: 'No files found to search',
                    metadata: {
                        pattern,
                        filesSearched: 0,
                        matchingFiles: 0,
                        executionTime: Date.now() - startTime
                    }
                };
            }
            // Search files
            const results = [];
            let filesSearched = 0;
            for (const filePath of filesToSearch) {
                try {
                    const fileResult = await this.searchFile(filePath, regex);
                    filesSearched++;
                    if (fileResult.matches.length > 0) {
                        results.push(fileResult);
                        // Stop if we've reached max results
                        if (results.length >= maxResults) {
                            break;
                        }
                    }
                }
                catch (error) {
                    // Skip files that can't be read (binary, permissions, etc.)
                    continue;
                }
            }
            // Sort results by modification time (newest first)
            results.sort((a, b) => b.modifiedTime.getTime() - a.modifiedTime.getTime());
            // Format output
            const output = this.formatResults(results, pattern);
            const executionTime = Date.now() - startTime;
            return {
                success: true,
                output,
                metadata: {
                    pattern,
                    filesSearched,
                    matchingFiles: results.length,
                    totalMatches: results.reduce((sum, r) => sum + r.matches.length, 0),
                    executionTime,
                    searchPaths: paths,
                    recursive,
                    ignoreCase
                }
            };
        }
        catch (error) {
            return {
                success: false,
                output: '',
                error: `Search failed: ${error instanceof Error ? error.message : String(error)}`,
                metadata: {
                    pattern: options.pattern,
                    executionTime: Date.now() - startTime
                }
            };
        }
    }
    async findFilesToSearch(paths, recursive) {
        const allFiles = [];
        for (const searchPath of paths) {
            try {
                const resolvedPath = path_1.default.resolve(searchPath);
                const stat = await fs_1.promises.stat(resolvedPath);
                if (stat.isFile()) {
                    allFiles.push(resolvedPath);
                }
                else if (stat.isDirectory()) {
                    const pattern = recursive ? '**/*' : '*';
                    const files = await (0, fast_glob_1.glob)(pattern, {
                        cwd: resolvedPath,
                        absolute: true,
                        onlyFiles: true,
                        ignore: [
                            '**/node_modules/**',
                            '**/.git/**',
                            '**/dist/**',
                            '**/build/**',
                            '**/*.min.js',
                            '**/*.map',
                            '**/coverage/**',
                            '**/.next/**',
                            '**/.nuxt/**'
                        ]
                    });
                    allFiles.push(...files);
                }
            }
            catch (error) {
                // Skip paths that can't be accessed
                continue;
            }
        }
        return [...new Set(allFiles)]; // Remove duplicates
    }
    async searchFile(filePath, regex) {
        const content = await fs_1.promises.readFile(filePath, 'utf8');
        const lines = content.split('\n');
        const matches = [];
        const stat = await fs_1.promises.stat(filePath);
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            let match;
            // Reset regex lastIndex for global regex
            regex.lastIndex = 0;
            while (line && (match = regex.exec(line)) !== null) {
                matches.push({
                    file: filePath,
                    lineNumber: i + 1,
                    line: line || '',
                    matchStart: match.index,
                    matchEnd: match.index + match[0].length
                });
                // Prevent infinite loop with zero-length matches
                if (match.index === regex.lastIndex) {
                    regex.lastIndex++;
                }
            }
        }
        return {
            file: filePath,
            matches,
            modifiedTime: stat.mtime
        };
    }
    formatResults(results, pattern) {
        if (results.length === 0) {
            return `No matches found for pattern: ${pattern}`;
        }
        const output = [];
        output.push(`Found ${results.length} files with matches for pattern: ${pattern}\n`);
        for (const result of results) {
            const relativePath = path_1.default.relative(process.cwd(), result.file);
            output.push(`📁 ${relativePath} (${result.matches.length} matches)`);
            // Show first few matches per file
            const matchesToShow = result.matches.slice(0, 5);
            for (const match of matchesToShow) {
                const linePreview = match.line.length > 100
                    ? match.line.substring(0, 100) + '...'
                    : match.line;
                output.push(`   ${match.lineNumber}: ${linePreview.trim()}`);
            }
            if (result.matches.length > 5) {
                output.push(`   ... and ${result.matches.length - 5} more matches`);
            }
            output.push(''); // Empty line between files
        }
        return output.join('\n');
    }
}
exports.GrepTool = GrepTool;
//# sourceMappingURL=grep.js.map